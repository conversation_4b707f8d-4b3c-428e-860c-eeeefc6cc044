-- 个人年度绩效计划主表
DROP TABLE IF EXISTS `personnel_yearly_plan`;
CREATE TABLE `personnel_yearly_plan` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `department` varchar(100) NOT NULL COMMENT '科室',
  `year` int NOT NULL COMMENT '年份',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_dept_year` (`name`, `department`, `year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划主表';

-- 个人年度绩效计划个性指标表
DROP TABLE IF EXISTS `personnel_yearly_personal_indicator`;
CREATE TABLE `personnel_yearly_personal_indicator` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID',
  `seq_no` int DEFAULT NULL COMMENT '序号',
  `task_category` varchar(200) DEFAULT NULL COMMENT '任务类别',
  `performance_task` text COMMENT '绩效任务',
  `target_measures` text COMMENT '目标及措施',
  `evaluation_criteria` text COMMENT '评价标准',
  `score_weight` varchar(50) DEFAULT NULL COMMENT '分值或权重',
  `responsibility_category` varchar(200) DEFAULT NULL COMMENT '责任分类',
  `completion_time` varchar(100) DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  CONSTRAINT `fk_personal_plan_id` FOREIGN KEY (`plan_id`) REFERENCES `personnel_yearly_plan` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划个性指标表';

-- 个人年度绩效计划共性指标表
DROP TABLE IF EXISTS `personnel_yearly_common_indicator`;
CREATE TABLE `personnel_yearly_common_indicator` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID',
  `seq_no` int DEFAULT NULL COMMENT '序号',
  `responsibility_category` varchar(500) DEFAULT NULL COMMENT '责任分类',
  `score_weight` varchar(50) DEFAULT NULL COMMENT '分值权重',
  `completion_time` varchar(100) DEFAULT NULL COMMENT '完成时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  CONSTRAINT `fk_common_plan_id` FOREIGN KEY (`plan_id`) REFERENCES `personnel_yearly_plan` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划共性指标表';
