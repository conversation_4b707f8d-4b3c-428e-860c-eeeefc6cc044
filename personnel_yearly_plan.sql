-- =============================================
-- 个人年度绩效计划数据库表结构
-- =============================================

-- 删除外键约束（如果存在）
SET FOREIGN_KEY_CHECKS = 0;

-- 删除已存在的表
DROP TABLE IF EXISTS `personnel_yearly_common_indicator`;
DROP TABLE IF EXISTS `personnel_yearly_personal_indicator`;
DROP TABLE IF EXISTS `personnel_yearly_plan`;

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 1. 个人年度绩效计划主表
-- =============================================
CREATE TABLE `personnel_yearly_plan` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '姓名',
  `department` varchar(100) NOT NULL COMMENT '科室',
  `year` int NOT NULL COMMENT '年份',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name_dept_year` (`name`, `department`, `year`),
  KEY `idx_name` (`name`),
  KEY `idx_department` (`department`),
  KEY `idx_year` (`year`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划主表';

-- =============================================
-- 2. 个人年度绩效计划个性指标表
-- 对应Excel列：序号、任务类型、绩效任务、目标及措施、评价标准、分值或权重、责任分类、完成时限
-- =============================================
CREATE TABLE `personnel_yearly_personal_indicator` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID，关联personnel_yearly_plan.id',
  `seq_no` int DEFAULT NULL COMMENT '序号',
  `task_category` varchar(200) DEFAULT NULL COMMENT '任务类型',
  `performance_task` text COMMENT '绩效任务',
  `target_measures` text COMMENT '目标及措施',
  `evaluation_criteria` text COMMENT '评价标准',
  `score_weight` varchar(50) DEFAULT NULL COMMENT '分值或权重',
  `responsibility_category` varchar(200) DEFAULT NULL COMMENT '责任分类',
  `completion_time` varchar(100) DEFAULT NULL COMMENT '完成时限',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_seq_no` (`seq_no`),
  CONSTRAINT `fk_personal_plan_id` FOREIGN KEY (`plan_id`) REFERENCES `personnel_yearly_plan` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划个性指标表';

-- =============================================
-- 3. 个人年度绩效计划共性指标表
-- 共性指标：政治表现、能力素质、精神状态、工作作风、廉洁自律
-- =============================================
CREATE TABLE `personnel_yearly_common_indicator` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID，关联personnel_yearly_plan.id',
  `seq_no` int DEFAULT NULL COMMENT '序号',
  `responsibility_category` varchar(500) DEFAULT NULL COMMENT '责任分类（政治表现、能力素质、精神状态、工作作风、廉洁自律）',
  `score_weight` varchar(50) DEFAULT NULL COMMENT '分值或权重',
  `completion_time` varchar(100) DEFAULT NULL COMMENT '完成时限',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_seq_no` (`seq_no`),
  CONSTRAINT `fk_common_plan_id` FOREIGN KEY (`plan_id`) REFERENCES `personnel_yearly_plan` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='个人年度绩效计划共性指标表';

-- =============================================
-- 插入示例数据（可选）
-- =============================================
-- INSERT INTO `personnel_yearly_plan` (`name`, `department`, `year`) VALUES ('张三', '综合科', 2025);
-- INSERT INTO `personnel_yearly_plan` (`name`, `department`, `year`) VALUES ('李四', '财务科', 2025);

-- =============================================
-- 查看表结构
-- =============================================
-- SHOW CREATE TABLE personnel_yearly_plan;
-- SHOW CREATE TABLE personnel_yearly_personal_indicator;
-- SHOW CREATE TABLE personnel_yearly_common_indicator;
