package com.ruoyi.web.controller.performance;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.PerformancePlanLeader;
import com.ruoyi.system.service.IPerformancePlanLeaderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/performance/leader")
public class PerformancePlanLeaderController extends BaseController {
    @Autowired
    private IPerformancePlanLeaderService service;

    /**
     * 查询班子成员绩效计划列表
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:list')")
    @GetMapping("/list")
    public TableDataInfo list(PerformancePlanLeader plan) {
        startPage();
        List<PerformancePlanLeader> list = service.selectPerformancePlanLeaderList(plan);
        return getDataTable(list);
    }

    /**
     * 获取班子成员绩效计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectPerformancePlanLeaderById(id));
    }

    /**
     * 新增班子成员绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:add')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PerformancePlanLeader plan) {
        return toAjax(service.insertPerformancePlanLeader(plan));
    }

    /**
     * 修改班子成员绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:edit')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PerformancePlanLeader plan) {
        return toAjax(service.updatePerformancePlanLeader(plan));
    }

    /**
     * 删除班子成员绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:remove')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deletePerformancePlanLeaderByIds(ids));
    }

    /**
     * 导入Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:import')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importWord(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importWord(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 导出单个Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:export')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export/{id}")
    public void exportWord(@PathVariable("id") Long id, HttpServletResponse response) throws Exception {
        service.exportWord(new Long[]{id}, response);
    }

    /**
     * 批量导出Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:export')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.EXPORT)
    @PostMapping("/batchExport")
    public void batchExportWord(@RequestBody Long[] ids, HttpServletResponse response) throws Exception {
        service.exportWord(ids, response);
    }

    /**
     * 下载模板
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:list')")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws Exception {
        service.exportTemplate(response);
    }

    /**
     * 导入Excel文档
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:import')")
    @Log(title = "班子成员绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importExcel")
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importExcelData(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载Excel模板
     */
    @PreAuthorize("@ss.hasPermi('performance:leader:list')")
    @GetMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        service.downloadExcelTemplate(response);
    }
}