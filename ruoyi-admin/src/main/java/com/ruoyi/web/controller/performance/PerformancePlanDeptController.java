package com.ruoyi.web.controller.performance;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.PerformancePlanDept;
import com.ruoyi.system.service.IPerformancePlanDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/performance/plan/dept")
public class PerformancePlanDeptController extends BaseController {
    @Autowired
    private IPerformancePlanDeptService service;

    /**
     * 查询科室绩效计划列表
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:list')")
    @GetMapping("/list")
    public TableDataInfo list(PerformancePlanDept planDept) {
        startPage();
        List<PerformancePlanDept> list = service.selectPerformancePlanDeptList(planDept);
        return getDataTable(list);
    }

    /**
     * 获取科室绩效计划详细信息
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:query')")
    @GetMapping("/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(service.selectPerformancePlanDeptById(id));
    }

    /**
     * 新增科室绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:add')")
    @Log(title = "科室绩效计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PerformancePlanDept planDept) {
        return toAjax(service.insertPerformancePlanDept(planDept));
    }

    /**
     * 修改科室绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:edit')")
    @Log(title = "科室绩效计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PerformancePlanDept planDept) {
        return toAjax(service.updatePerformancePlanDept(planDept));
    }

    /**
     * 删除科室绩效计划
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:remove')")
    @Log(title = "科室绩效计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(service.deletePerformancePlanDeptByIds(ids));
    }

    /**
     * 导出单个Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:export')")
    @Log(title = "科室绩效计划", businessType = BusinessType.EXPORT)
    @GetMapping("/export/{id}")
    public void exportWord(@PathVariable("id") Long id, HttpServletResponse response) throws Exception {
        service.exportWord(new Long[]{id}, response);
    }

    /**
     * 批量导出Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:export')")
    @Log(title = "科室绩效计划", businessType = BusinessType.EXPORT)
    @PostMapping("/batchExport")
    public void batchExportWord(@RequestBody Long[] ids, HttpServletResponse response) throws Exception {
        service.exportWord(ids, response);
    }

    /**
     * 导入Word文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:import')")
    @Log(title = "科室绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importWord(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importWord(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载Word模板
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:list')")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws Exception {
        service.exportTemplate(response);
    }

    /**
     * 导入Excel文档
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:import')")
    @Log(title = "科室绩效计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importExcel")
    public AjaxResult importExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return AjaxResult.error("请选择文件");
        }
        try {
            service.importExcelData(file);
            return AjaxResult.success("导入成功");
        } catch (Exception e) {
            return AjaxResult.error("导入失败: " + e.getMessage());
        }
    }

    /**
     * 下载Excel模板
     */
    @PreAuthorize("@ss.hasPermi('performance:plan:dept:list')")
    @GetMapping("/downloadExcelTemplate")
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        service.downloadExcelTemplate(response);
    }
}