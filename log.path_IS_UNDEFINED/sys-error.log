21:21:01.493 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

21:22:05.307 [restartedMain] ERROR o.s.b.d.LoggingFailureAnalysisReporter - [report,40] - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

21:49:12.926 [restartedMain] ERROR o.s.b.SpringApplication - [reportFailure,870] - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'personnelYearlyPlanServiceImpl' defined in file [/Users/<USER>/Desktop/dev/performance/ruoyi-system/target/classes/com/ruoyi/system/service/impl/PersonnelYearlyPlanServiceImpl.class]: Instantiation of bean failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.ruoyi.system.service.impl.PersonnelYearlyPlanServiceImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The type PersonnelYearlyPlanServiceImpl must implement the inherited abstract method IPersonnelYearlyPlanService.downloadExcelTemplate(HttpServletResponse)
	The method extractNameAndDepartment(Sheet) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parsePersonalIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parseCommonIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	Syntax error, insert "}" to complete ClassBody

	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1334)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:336)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:334)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:209)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:932)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:780)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:453)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:343)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1370)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1359)
	at com.ruoyi.RuoYiApplication.main(RuoYiApplication.java:21)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:49)
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.ruoyi.system.service.impl.PersonnelYearlyPlanServiceImpl]: Constructor threw exception; nested exception is java.lang.Error: Unresolved compilation problems: 
	The type PersonnelYearlyPlanServiceImpl must implement the inherited abstract method IPersonnelYearlyPlanService.downloadExcelTemplate(HttpServletResponse)
	The method extractNameAndDepartment(Sheet) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parsePersonalIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parseCommonIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	Syntax error, insert "}" to complete ClassBody

	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:226)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1326)
	... 22 common frames omitted
Caused by: java.lang.Error: Unresolved compilation problems: 
	The type PersonnelYearlyPlanServiceImpl must implement the inherited abstract method IPersonnelYearlyPlanService.downloadExcelTemplate(HttpServletResponse)
	The method extractNameAndDepartment(Sheet) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parsePersonalIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	The method parseCommonIndicators(Sheet, Long) is undefined for the type PersonnelYearlyPlanServiceImpl
	Syntax error, insert "}" to complete ClassBody

	at com.ruoyi.system.service.impl.PersonnelYearlyPlanServiceImpl.<init>(PersonnelYearlyPlanServiceImpl.java:27)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:213)
	... 24 common frames omitted
