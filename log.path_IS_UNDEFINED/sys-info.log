21:20:31.750 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:20:56.466 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:20:56.468 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 24584 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by ma<PERSON><PERSON> in /Users/<USER>/Desktop/dev/performance)
21:20:56.468 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:20:58.538 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:20:58.539 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:20:58.539 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:20:58.579 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:20:59.246 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:21:01.338 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:21:01.344 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:21:01.346 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:21:01.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:21:01.465 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8080"]
21:21:01.465 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:21:01.467 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8080"]
21:21:01.467 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8080"]
21:22:01.644 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25238 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:22:01.644 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:22:01.646 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:22:02.658 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:22:02.659 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:22:02.659 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:22:02.688 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:22:03.271 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:22:05.176 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:22:05.181 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:22:05.182 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:22:05.185 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:22:05.296 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Pausing ProtocolHandler ["http-nio-8080"]
21:22:05.296 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:22:05.297 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Stopping ProtocolHandler ["http-nio-8080"]
21:22:05.298 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Destroying ProtocolHandler ["http-nio-8080"]
21:22:53.168 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
21:22:53.168 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:22:53.169 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:22:54.036 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
21:22:54.037 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:22:54.037 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:22:54.064 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:22:54.564 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
21:22:56.294 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
21:22:56.420 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.397 seconds (JVM running for 4.192)
21:23:07.234 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
21:23:07.240 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:23:25.161 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:41:13.497 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:41:13.500 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
21:41:13.523 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
21:41:16.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:41:16.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:41:17.089 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:41:17.089 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:41:17.094 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:41:17.525 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
21:41:18.972 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.579 seconds (JVM running for 1106.719)
21:42:10.139 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:46:47.875 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:46:47.877 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
21:46:47.880 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
21:46:48.237 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:46:48.237 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:46:48.570 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:46:48.570 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:46:48.573 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:46:48.872 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
21:46:50.040 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.824 seconds (JVM running for 1437.78)
21:47:02.766 [Thread-26] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:02.770 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
21:47:02.773 [Thread-26] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
21:47:03.183 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:03.184 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:03.498 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:03.498 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:03.500 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:03.703 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
21:47:04.702 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.537 seconds (JVM running for 1452.441)
21:47:15.347 [Thread-30] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:15.353 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
21:47:15.362 [Thread-30] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
21:47:15.762 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:15.762 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:16.084 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:16.084 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:16.087 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:16.294 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
21:47:17.848 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.104 seconds (JVM running for 1465.587)
21:47:26.448 [Thread-34] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:26.451 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
21:47:26.454 [Thread-34] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
21:47:26.879 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:26.879 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:27.159 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:27.159 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:27.161 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:27.406 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-6} inited
21:47:28.589 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.726 seconds (JVM running for 1476.328)
21:47:38.240 [Thread-38] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:38.254 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-6} closing ...
21:47:38.258 [Thread-38] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-6} closed
21:47:38.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:38.666 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:38.999 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:38.999 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:39.002 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:39.223 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-7} inited
21:47:40.317 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.668 seconds (JVM running for 1488.056)
21:47:56.148 [Thread-42] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:47:56.153 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
21:47:56.157 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
21:47:56.655 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:47:56.655 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:47:56.989 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:47:56.989 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:47:56.991 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:47:57.205 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
21:47:58.274 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.636 seconds (JVM running for 1506.012)
21:48:17.211 [Thread-46] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:48:17.215 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
21:48:17.217 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
21:48:17.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:48:17.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:48:17.975 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:48:17.975 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:48:17.985 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:48:18.224 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
21:48:19.525 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.878 seconds (JVM running for 1527.263)
21:48:48.773 [Thread-50] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:48:48.777 [Thread-50] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
21:48:48.781 [Thread-50] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
21:48:49.170 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:48:49.170 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:48:49.482 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:48:49.482 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:48:49.484 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:48:49.711 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-10} inited
21:48:50.779 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.625 seconds (JVM running for 1558.516)
21:49:10.763 [Thread-54] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:10.768 [Thread-54] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-10} closing ...
21:49:10.771 [Thread-54] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-10} closed
21:49:11.330 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:11.330 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:11.901 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:11.901 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:11.904 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:12.161 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-11} inited
21:49:12.802 [restartedMain] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:12.802 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-11} closing ...
21:49:12.805 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-11} closed
21:49:12.913 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:49:42.219 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:42.219 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:42.547 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:42.547 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:42.550 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:42.755 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-12} inited
21:49:43.862 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.674 seconds (JVM running for 1611.598)
21:50:09.934 [Thread-58] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:09.939 [Thread-58] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-12} closing ...
21:50:09.944 [Thread-58] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-12} closed
21:50:10.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:10.595 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:11.172 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:11.172 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:11.176 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:11.453 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-13} inited
21:50:13.070 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.499 seconds (JVM running for 1640.806)
21:50:28.487 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:50:39.163 [Thread-65] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:39.166 [Thread-65] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-13} closing ...
21:50:39.169 [Thread-65] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-13} closed
21:50:39.916 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:39.916 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:40.413 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:40.414 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:40.419 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:40.675 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-14} inited
21:50:41.938 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.043 seconds (JVM running for 1669.673)
21:51:04.937 [Thread-69] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:51:04.941 [Thread-69] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-14} closing ...
21:51:04.944 [Thread-69] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-14} closed
21:51:05.503 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:51:05.503 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:51:05.845 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:51:05.845 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:51:05.854 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:51:06.099 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-15} inited
21:51:07.532 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.046 seconds (JVM running for 1695.266)
21:51:37.042 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:58:04.538 [Thread-73] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:58:04.550 [Thread-73] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-15} closing ...
21:58:04.568 [Thread-73] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-15} closed
21:58:05.394 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:58:05.394 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:58:05.825 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:58:05.825 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:58:05.831 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:58:06.102 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-16} inited
21:58:07.663 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.307 seconds (JVM running for 2115.39)
21:59:03.091 [Thread-77] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:59:03.097 [Thread-77] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-16} closing ...
21:59:03.102 [Thread-77] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-16} closed
21:59:03.937 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:59:03.937 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:59:04.350 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:59:04.350 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:59:04.356 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:59:04.617 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-17} inited
21:59:06.278 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.36 seconds (JVM running for 2174.004)
21:59:11.656 [Thread-81] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:59:11.660 [Thread-81] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-17} closing ...
21:59:11.662 [Thread-81] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-17} closed
21:59:12.497 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:59:12.497 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:59:12.861 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:59:12.861 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:59:12.870 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:59:13.121 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-18} inited
21:59:14.499 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.025 seconds (JVM running for 2182.224)
22:00:14.986 [Thread-85] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:00:14.993 [Thread-85] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-18} closing ...
22:00:14.997 [Thread-85] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-18} closed
22:00:15.477 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:00:15.477 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:00:15.832 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:00:15.832 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:00:15.838 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:00:16.087 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-19} inited
22:00:17.399 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.94 seconds (JVM running for 2245.123)
22:00:35.272 [Thread-89] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:00:35.276 [Thread-89] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-19} closing ...
22:00:35.280 [Thread-89] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-19} closed
22:00:35.846 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:00:35.846 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:00:36.213 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:00:36.213 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:00:36.216 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:00:36.455 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-20} inited
22:00:37.770 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.944 seconds (JVM running for 2265.493)
22:01:02.805 [Thread-93] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:01:02.810 [Thread-93] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-20} closing ...
22:01:02.815 [Thread-93] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-20} closed
22:01:03.341 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:01:03.342 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:01:03.701 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:01:03.701 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:01:03.703 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:01:03.958 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-21} inited
22:01:05.206 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.883 seconds (JVM running for 2292.929)
22:01:28.346 [Thread-97] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:01:28.352 [Thread-97] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-21} closing ...
22:01:28.356 [Thread-97] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-21} closed
22:01:28.909 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:01:28.909 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:01:29.248 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:01:29.248 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:01:29.250 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:01:29.500 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-22} inited
22:01:30.842 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.952 seconds (JVM running for 2318.564)
22:01:46.665 [Thread-101] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:01:46.669 [Thread-101] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-22} closing ...
22:01:46.672 [Thread-101] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-22} closed
22:01:47.252 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:01:47.252 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:01:47.566 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:01:47.566 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:01:47.569 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:01:47.800 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-23} inited
22:01:49.269 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.036 seconds (JVM running for 2336.991)
22:01:57.891 [Thread-105] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:01:57.897 [Thread-105] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-23} closing ...
22:01:57.901 [Thread-105] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-23} closed
22:01:58.685 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:01:58.685 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:01:59.042 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:01:59.042 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:01:59.046 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:01:59.267 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-24} inited
22:02:00.522 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.858 seconds (JVM running for 2348.243)
22:02:15.557 [Thread-109] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:02:15.560 [Thread-109] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-24} closing ...
22:02:15.563 [Thread-109] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-24} closed
22:02:16.161 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:02:16.161 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:02:16.542 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:02:16.542 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:02:16.544 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:02:16.783 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-25} inited
22:02:18.004 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.861 seconds (JVM running for 2365.725)
22:02:55.473 [Thread-113] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:02:55.478 [Thread-113] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-25} closing ...
22:02:55.485 [Thread-113] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-25} closed
22:02:56.065 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:02:56.065 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:02:56.432 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:02:56.433 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:02:56.440 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:02:56.728 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-26} inited
22:02:57.972 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.932 seconds (JVM running for 2405.692)
22:03:11.736 [Thread-117] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:03:11.741 [Thread-117] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-26} closing ...
22:03:11.745 [Thread-117] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-26} closed
22:03:12.236 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:03:12.236 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:03:12.573 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:03:12.573 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:03:12.577 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:03:12.799 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-27} inited
22:03:14.004 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.784 seconds (JVM running for 2421.724)
22:03:32.931 [Thread-121] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:03:32.936 [Thread-121] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-27} closing ...
22:03:32.940 [Thread-121] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-27} closed
22:03:33.549 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:03:33.549 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:03:33.871 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:03:33.871 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:03:33.877 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:03:34.099 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-28} inited
22:03:35.275 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.741 seconds (JVM running for 2442.994)
22:04:08.754 [Thread-125] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:04:08.757 [Thread-125] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-28} closing ...
22:04:08.761 [Thread-125] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-28} closed
22:04:09.707 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:04:09.708 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:04:10.306 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:04:10.307 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:04:10.314 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:04:10.583 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-29} inited
22:04:11.842 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.177 seconds (JVM running for 2479.561)
22:04:23.547 [Thread-129] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:04:23.553 [Thread-129] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-29} closing ...
22:04:23.557 [Thread-129] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-29} closed
22:04:24.218 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:04:24.218 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:04:24.704 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:04:24.705 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:04:24.711 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:04:25.250 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-30} inited
22:04:26.541 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.341 seconds (JVM running for 2494.259)
22:08:27.957 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:08:53.900 [Thread-133] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:08:53.914 [Thread-133] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-30} closing ...
22:08:53.928 [Thread-133] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-30} closed
22:08:54.591 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:08:54.591 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:08:55.075 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:08:55.075 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:08:55.079 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:08:55.384 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-31} inited
22:08:57.544 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.979 seconds (JVM running for 2765.256)
22:09:04.631 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:16:22.404 [Thread-137] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:16:22.407 [Thread-137] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-31} closing ...
22:16:22.411 [Thread-137] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-31} closed
22:16:23.582 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:23.582 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:23.976 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:23.976 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:23.981 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:24.322 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-32} inited
22:16:26.365 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.804 seconds (JVM running for 3214.067)
22:16:29.878 [Thread-141] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:16:29.881 [Thread-141] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-32} closing ...
22:16:29.884 [Thread-141] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-32} closed
22:16:30.756 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:30.756 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:31.448 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:31.449 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:31.456 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:31.772 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-33} inited
22:16:33.500 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.765 seconds (JVM running for 3221.202)
22:16:43.032 [Thread-145] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:16:43.039 [Thread-145] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-33} closing ...
22:16:43.049 [Thread-145] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-33} closed
22:16:43.893 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:43.894 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:44.280 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:44.281 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:44.284 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:44.549 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-34} inited
22:16:45.783 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.905 seconds (JVM running for 3233.484)
22:16:52.251 [Thread-149] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:16:52.255 [Thread-149] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-34} closing ...
22:16:52.257 [Thread-149] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-34} closed
22:16:52.944 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:16:52.944 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:16:53.304 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:16:53.304 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:16:53.308 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:16:53.578 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-35} inited
22:16:55.034 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.108 seconds (JVM running for 3242.736)
22:17:02.722 [Thread-153] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:17:02.726 [Thread-153] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-35} closing ...
22:17:02.728 [Thread-153] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-35} closed
22:17:03.220 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:03.220 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:03.563 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:03.563 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:03.566 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:03.781 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-36} inited
22:17:05.951 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.747 seconds (JVM running for 3253.652)
22:17:09.816 [Thread-157] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:17:09.821 [Thread-157] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-36} closing ...
22:17:09.834 [Thread-157] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-36} closed
22:17:10.790 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:10.790 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:11.264 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:11.265 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:11.269 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:11.690 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-37} inited
22:17:12.457 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-37} closing ...
22:17:12.459 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-37} closed
22:17:12.566 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:17:14.080 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:14.080 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:14.446 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:14.446 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:14.449 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:14.995 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-38} inited
22:17:17.040 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.979 seconds (JVM running for 3264.741)
22:17:26.551 [Thread-161] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:17:26.558 [Thread-161] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-38} closing ...
22:17:26.563 [Thread-161] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-38} closed
22:17:28.457 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:28.458 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:30.547 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:30.548 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:30.552 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:30.816 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-39} inited
22:17:32.420 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 4.003 seconds (JVM running for 3280.121)
22:17:35.766 [Thread-168] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:17:35.769 [Thread-168] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-39} closing ...
22:17:35.772 [Thread-168] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-39} closed
22:17:36.279 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:36.279 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:36.581 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:36.581 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:36.584 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:36.805 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-40} inited
22:17:37.976 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.717 seconds (JVM running for 3285.676)
22:17:46.224 [Thread-172] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:17:46.229 [Thread-172] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-40} closing ...
22:17:46.232 [Thread-172] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-40} closed
22:17:46.748 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:17:46.748 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:17:47.086 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:17:47.086 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:17:47.089 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:17:47.321 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-41} inited
22:17:48.631 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.906 seconds (JVM running for 3296.331)
22:18:35.777 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:20:15.317 [Thread-176] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:20:15.332 [Thread-176] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-41} closing ...
22:20:15.351 [Thread-176] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-41} closed
22:20:16.278 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:20:16.279 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:20:16.713 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:20:16.714 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:20:16.717 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:20:16.990 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-42} inited
22:20:18.540 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.3 seconds (JVM running for 3446.237)
22:20:20.346 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:23:05.815 [Thread-180] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:23:05.818 [Thread-180] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-42} closing ...
22:23:05.820 [Thread-180] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-42} closed
22:23:07.231 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:23:07.231 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:23:07.671 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:23:07.672 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:23:07.677 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:23:07.953 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-43} inited
22:23:09.652 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.452 seconds (JVM running for 3617.346)
22:23:16.653 [Thread-184] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:23:16.657 [Thread-184] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-43} closing ...
22:23:16.660 [Thread-184] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-43} closed
22:23:17.816 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:23:17.816 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:23:18.231 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:23:18.231 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:23:18.234 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:23:18.511 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-44} inited
22:23:20.296 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.508 seconds (JVM running for 3627.989)
22:23:21.044 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:28:30.230 [Thread-188] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:28:30.231 [Thread-188] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-44} closing ...
22:28:30.234 [Thread-188] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-44} closed
22:28:31.657 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 25415 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:28:31.658 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:28:32.208 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:28:32.209 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:28:32.214 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:28:34.756 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-45} inited
22:28:35.211 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-45} closing ...
22:28:35.213 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-45} closed
22:28:35.321 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:28:53.482 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:28:53.483 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:28:53.483 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:28:54.374 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:28:54.375 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:28:54.375 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:28:54.402 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:28:54.915 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
22:28:56.622 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:28:56.742 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.435 seconds (JVM running for 4.694)
22:29:00.990 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
22:29:00.994 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:35:44.144 [Thread-11] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:35:44.146 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
22:35:44.151 [Thread-11] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
22:35:44.416 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:35:44.416 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:35:44.778 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:35:44.778 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:35:44.778 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:35:44.781 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:35:45.073 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
22:35:46.342 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:35:46.392 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.002 seconds (JVM running for 414.336)
22:35:47.447 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:38:08.121 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:38:08.123 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
22:38:08.125 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
22:38:08.548 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:38:08.548 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:38:08.963 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:38:08.963 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:38:08.963 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:38:08.967 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:38:09.204 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
22:38:10.774 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:38:10.846 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.334 seconds (JVM running for 558.787)
22:38:11.796 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:39:05.132 [Thread-23] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:39:05.134 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
22:39:05.137 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
22:39:05.538 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:39:05.538 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:39:05.947 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:39:05.947 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:39:05.947 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:39:05.950 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:39:06.185 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
22:39:07.355 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:39:07.417 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.902 seconds (JVM running for 615.356)
22:39:17.912 [Thread-27] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:39:17.916 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
22:39:17.919 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
22:39:18.271 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:39:18.271 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:39:18.613 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:39:18.613 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:39:18.613 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:39:18.616 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:39:18.845 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
22:39:20.014 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:39:20.058 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.806 seconds (JVM running for 627.997)
22:39:24.464 [Thread-31] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:39:24.468 [Thread-31] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
22:39:24.471 [Thread-31] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
22:39:25.039 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:39:25.039 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:39:25.351 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:39:25.351 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:39:25.352 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:39:25.354 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:39:25.565 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-6} inited
22:39:26.766 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:39:26.811 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.793 seconds (JVM running for 634.75)
22:40:08.652 [Thread-35] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:40:08.655 [Thread-35] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-6} closing ...
22:40:08.657 [Thread-35] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-6} closed
22:40:09.162 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 43585 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:40:09.162 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:40:09.906 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-7} inited
22:40:11.150 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.013 seconds (JVM running for 679.087)
22:40:12.163 [SpringApplicationShutdownHook] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:40:12.164 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
22:40:12.166 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
22:40:14.686 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:40:14.686 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45985 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:40:14.687 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:40:15.649 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:40:15.650 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:40:15.650 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:40:15.677 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:40:16.202 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
22:40:17.941 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:40:18.068 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.55 seconds (JVM running for 4.927)
22:40:22.736 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
22:40:22.739 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:41:24.573 [Thread-12] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:41:24.575 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
22:41:24.581 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
22:41:24.996 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45985 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:41:24.996 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:41:25.497 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:41:25.498 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:41:25.498 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:41:25.501 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:41:25.769 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
22:41:26.985 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:41:27.032 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.071 seconds (JVM running for 73.889)
22:41:28.777 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:41:28.780 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
22:41:28.783 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
22:41:29.088 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45985 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:41:29.088 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:41:29.361 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:41:29.361 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:41:29.361 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:41:29.364 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:41:29.569 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
22:41:30.605 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:41:30.649 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.579 seconds (JVM running for 77.507)
22:41:37.419 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:43:16.749 [Thread-23] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:43:16.751 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
22:43:16.753 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
22:43:17.378 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45985 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:43:17.378 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:43:17.727 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:43:17.727 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:43:17.727 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:43:17.731 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:43:18.096 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
22:43:19.465 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:43:19.521 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.166 seconds (JVM running for 186.376)
