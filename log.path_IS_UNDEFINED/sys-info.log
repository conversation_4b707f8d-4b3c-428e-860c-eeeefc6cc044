21:01:54.544 [Thread-42] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:01:54.583 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
21:01:54.586 [Thread-42] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
21:01:55.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:01:55.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:02:00.285 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:02:00.286 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:02:01.107 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:02:01.107 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:02:01.112 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:02:01.558 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
21:02:03.254 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.016 seconds (JVM running for 500530.506)
21:03:09.844 [Thread-46] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:03:09.850 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
21:03:09.855 [Thread-46] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
21:03:10.510 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:03:10.510 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:03:11.016 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:03:11.016 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:03:11.019 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:03:11.345 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
21:03:12.644 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.179 seconds (JVM running for 500599.897)
21:07:53.111 [Thread-52] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:07:53.122 [Thread-52] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
21:07:53.145 [Thread-52] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
21:07:53.881 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:07:53.881 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:07:54.758 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:07:54.759 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:07:54.770 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:07:54.813 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
21:07:57.215 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:07:57.216 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:07:58.104 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:07:58.104 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:07:58.117 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:07:58.589 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-10} inited
21:08:01.355 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 4.196 seconds (JVM running for 500888.611)
21:39:42.160 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
21:39:42.170 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:39:47.970 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[admin][Success][登录成功]
21:49:01.963 [Thread-56] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:01.971 [Thread-56] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-10} closing ...
21:49:01.975 [Thread-56] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-10} closed
21:49:02.890 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:02.891 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:03.384 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:03.384 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:03.389 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:03.716 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-11} inited
21:49:05.189 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.354 seconds (JVM running for 502943.473)
21:49:12.096 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:49:39.599 [Thread-63] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:39.601 [Thread-63] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-11} closing ...
21:49:39.604 [Thread-63] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-11} closed
21:49:40.100 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:40.100 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:40.539 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:40.539 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:40.542 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:40.807 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-12} inited
21:49:41.857 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.776 seconds (JVM running for 502980.134)
21:49:46.536 [Thread-67] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:49:46.541 [Thread-67] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-12} closing ...
21:49:46.554 [Thread-67] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-12} closed
21:49:47.052 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:49:47.053 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:49:47.462 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:49:47.462 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:49:47.465 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:49:47.731 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-13} inited
21:49:48.865 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.845 seconds (JVM running for 502987.141)
21:49:59.298 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:50:16.739 [Thread-71] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
21:50:16.742 [Thread-71] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-13} closing ...
21:50:16.747 [Thread-71] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-13} closed
21:50:17.247 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
21:50:17.247 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
21:50:17.728 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
21:50:17.728 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
21:50:17.731 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
21:50:18.029 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-14} inited
21:50:20.578 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.362 seconds (JVM running for 503018.854)
21:50:31.968 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:06:38.673 [Thread-75] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:06:38.674 [Thread-75] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-14} closing ...
22:06:38.677 [Thread-75] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-14} closed
22:06:39.294 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:06:39.294 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:06:39.750 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:06:39.750 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:06:39.753 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:06:40.135 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-15} inited
22:06:41.538 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.268 seconds (JVM running for 503999.818)
22:06:54.379 [Thread-79] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:06:54.384 [Thread-79] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-15} closing ...
22:06:54.391 [Thread-79] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-15} closed
22:06:55.062 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:06:55.062 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:06:55.444 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:06:55.444 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:06:55.448 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:06:55.726 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-16} inited
22:06:57.039 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.998 seconds (JVM running for 504015.32)
22:07:12.244 [Thread-83] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:07:12.254 [Thread-83] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-16} closing ...
22:07:12.258 [Thread-83] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-16} closed
22:07:12.956 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:07:12.956 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:13.614 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:07:13.614 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:07:13.617 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:07:13.866 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-17} inited
22:07:15.064 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.129 seconds (JVM running for 504033.345)
22:07:46.797 [Thread-87] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:07:46.800 [Thread-87] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-17} closing ...
22:07:46.804 [Thread-87] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-17} closed
22:07:47.429 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:07:47.429 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:07:47.979 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:07:47.979 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:07:47.983 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:07:48.490 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-18} inited
22:07:49.990 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.58 seconds (JVM running for 504068.271)
22:08:02.699 [Thread-91] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:08:02.704 [Thread-91] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-18} closing ...
22:08:02.709 [Thread-91] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-18} closed
22:08:03.466 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:08:03.467 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:08:04.084 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:08:04.084 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:08:04.089 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:08:04.350 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-19} inited
22:08:05.432 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.008 seconds (JVM running for 504083.713)
22:08:36.770 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:10:24.413 [Thread-95] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:10:24.415 [Thread-95] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-19} closing ...
22:10:24.417 [Thread-95] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-19} closed
22:10:25.094 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:10:25.094 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:10:25.591 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:10:25.591 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:10:25.596 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:10:25.953 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-20} inited
22:10:27.493 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.42 seconds (JVM running for 504225.775)
22:10:29.186 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:12:54.813 [Thread-99] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:12:54.814 [Thread-99] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-20} closing ...
22:12:54.825 [Thread-99] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-20} closed
22:12:55.500 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:12:55.501 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:12:55.933 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:12:55.933 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:12:55.938 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:12:56.280 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-21} inited
22:12:57.761 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.285 seconds (JVM running for 504376.044)
22:13:02.938 [Thread-103] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:13:02.945 [Thread-103] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-21} closing ...
22:13:02.949 [Thread-103] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-21} closed
22:13:04.270 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:13:04.270 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:13:04.754 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:13:04.754 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:13:04.758 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:13:05.005 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-22} inited
22:13:06.293 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.056 seconds (JVM running for 504384.576)
22:13:58.737 [Thread-107] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:13:58.742 [Thread-107] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-22} closing ...
22:13:58.746 [Thread-107] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-22} closed
22:13:59.672 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:13:59.673 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:14:00.527 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:14:00.527 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:14:00.541 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:14:01.021 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-23} inited
22:14:04.096 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 4.489 seconds (JVM running for 504442.379)
22:14:04.377 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:14:05.304 [Thread-111] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:14:05.313 [Thread-111] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-23} closing ...
22:14:05.317 [Thread-111] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-23} closed
22:14:06.088 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:14:06.089 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:14:06.946 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:14:06.946 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:14:06.951 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:14:07.246 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-24} inited
22:14:08.867 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.822 seconds (JVM running for 504447.15)
22:14:28.383 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:20:02.549 [Thread-115] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:20:02.552 [Thread-115] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-24} closing ...
22:20:02.555 [Thread-115] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-24} closed
22:20:03.166 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:20:03.167 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:20:03.725 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:20:03.725 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:20:03.733 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:20:03.996 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-25} inited
22:20:05.380 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.235 seconds (JVM running for 504803.717)
22:20:09.748 [Thread-119] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:20:09.751 [Thread-119] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-25} closing ...
22:20:09.756 [Thread-119] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-25} closed
22:20:10.530 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:20:10.530 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:20:10.901 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:20:10.901 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:20:10.904 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:20:11.145 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-26} inited
22:20:12.479 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.972 seconds (JVM running for 504810.816)
22:24:23.352 [Thread-123] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:24:23.355 [Thread-123] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-26} closing ...
22:24:23.362 [Thread-123] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-26} closed
22:24:24.227 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:24:24.227 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:24:24.779 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:24:24.779 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:24:24.787 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:24:25.170 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-27} inited
22:24:26.365 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.157 seconds (JVM running for 505064.706)
22:24:48.521 [Thread-127] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:24:48.526 [Thread-127] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-27} closing ...
22:24:48.533 [Thread-127] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-27} closed
22:24:49.344 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:24:49.344 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:24:49.703 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:24:49.703 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:24:49.705 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:24:49.951 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-28} inited
22:24:51.143 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.834 seconds (JVM running for 505089.485)
22:25:12.727 [Thread-131] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:25:12.731 [Thread-131] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-28} closing ...
22:25:12.734 [Thread-131] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-28} closed
22:25:13.540 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:25:13.540 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:25:13.919 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:25:13.919 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:25:13.922 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:25:14.200 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-29} inited
22:25:15.453 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.933 seconds (JVM running for 505113.795)
22:25:45.871 [Thread-135] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:25:45.874 [Thread-135] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-29} closing ...
22:25:45.878 [Thread-135] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-29} closed
22:25:46.469 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:25:46.469 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:25:46.802 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:25:46.802 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:25:46.805 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:25:47.033 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-30} inited
22:25:47.327 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-30} closing ...
22:25:47.328 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-30} closed
22:25:47.434 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:26:33.246 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:26:33.246 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:26:33.768 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:26:33.768 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:26:33.771 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:26:33.983 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-31} inited
22:26:35.175 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.959 seconds (JVM running for 505193.518)
22:26:59.235 [Thread-139] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:26:59.239 [Thread-139] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-31} closing ...
22:26:59.242 [Thread-139] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-31} closed
22:27:00.122 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:27:00.122 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:27:00.479 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:27:00.480 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:27:00.483 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:27:00.741 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-32} inited
22:27:01.811 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.708 seconds (JVM running for 505220.155)
22:28:57.789 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:31:59.918 [Thread-146] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:31:59.921 [Thread-146] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-32} closing ...
22:31:59.927 [Thread-146] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-32} closed
22:32:00.978 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:32:00.978 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:32:01.333 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:32:01.333 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:32:01.336 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:32:01.558 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-33} inited
22:32:02.734 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.775 seconds (JVM running for 505521.082)
22:32:12.726 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:32:37.170 [Thread-150] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:32:37.172 [Thread-150] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-33} closing ...
22:32:37.199 [Thread-150] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-33} closed
22:32:38.255 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:32:38.256 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:32:39.003 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:32:39.003 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:32:39.007 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:32:39.307 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-34} inited
22:32:40.919 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.719 seconds (JVM running for 505559.268)
22:33:11.091 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:33:52.657 [Thread-154] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:33:52.661 [Thread-154] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-34} closing ...
22:33:52.670 [Thread-154] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-34} closed
22:33:53.647 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 60268 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:33:53.647 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:33:54.440 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:33:54.442 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:33:54.450 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:33:54.901 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-35} inited
22:33:56.079 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-35} closing ...
22:33:56.085 [restartedMain] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-35} closed
22:33:56.195 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Stopping service [Tomcat]
22:34:23.070 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:34:23.071 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
22:34:23.072 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:34:24.081 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:34:24.082 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:34:24.082 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:34:24.109 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:34:24.640 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
22:34:26.346 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:34:26.466 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 3.634 seconds (JVM running for 4.875)
22:34:31.380 [http-nio-8080-exec-1] INFO  o.a.t.u.h.p.Cookie - [log,168] - A cookie header was received [Hm_lvt_a1ff8825baa73c3a78eb96aa40325abc=1750346069,1750383323,1750484999,1750832074;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
22:34:31.385 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:35:19.641 [Thread-12] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:35:19.643 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-1} closing ...
22:35:19.650 [Thread-12] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-1} closed
22:35:19.953 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:35:19.953 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:35:20.464 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:35:20.464 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:35:20.464 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:35:20.468 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:35:20.707 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
22:35:21.763 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:35:21.809 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.893 seconds (JVM running for 60.216)
22:35:22.348 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:39:41.160 [Thread-19] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:39:41.163 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-2} closing ...
22:39:41.170 [Thread-19] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-2} closed
22:39:41.708 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:39:41.708 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:39:42.153 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:39:42.153 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:39:42.153 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:39:42.160 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:39:42.418 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-3} inited
22:39:43.513 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:39:43.554 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.887 seconds (JVM running for 321.964)
22:39:45.931 [Thread-23] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:39:45.933 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-3} closing ...
22:39:45.935 [Thread-23] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-3} closed
22:39:46.395 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:39:46.395 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:39:46.697 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:39:46.697 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:39:46.697 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:39:46.700 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:39:46.924 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-4} inited
22:39:47.915 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:39:47.955 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.581 seconds (JVM running for 326.365)
22:46:16.329 [Thread-27] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:46:16.338 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-4} closing ...
22:46:16.345 [Thread-27] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-4} closed
22:46:16.708 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:46:16.709 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:46:17.080 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:46:17.081 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:46:17.081 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:46:17.083 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:46:17.313 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-5} inited
22:46:18.565 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:46:18.625 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.943 seconds (JVM running for 717.039)
22:46:32.475 [Thread-31] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:46:32.479 [Thread-31] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-5} closing ...
22:46:32.483 [Thread-31] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-5} closed
22:46:32.967 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:46:32.968 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:46:33.302 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:46:33.302 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:46:33.302 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:46:33.305 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:46:33.545 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-6} inited
22:46:35.069 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:46:35.116 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 2.167 seconds (JVM running for 733.531)
22:47:04.344 [Thread-35] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:47:04.347 [Thread-35] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-6} closing ...
22:47:04.351 [Thread-35] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-6} closed
22:47:04.909 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:47:04.909 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:47:05.291 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:47:05.291 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:47:05.291 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:47:05.294 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:47:05.505 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-7} inited
22:47:06.515 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:47:06.565 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.687 seconds (JVM running for 764.98)
22:47:27.555 [Thread-39] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:47:27.559 [Thread-39] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-7} closing ...
22:47:27.562 [Thread-39] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-7} closed
22:47:28.067 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:47:28.068 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:47:28.386 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:47:28.386 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:47:28.386 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:47:28.389 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:47:28.601 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-8} inited
22:47:29.652 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:47:29.702 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.655 seconds (JVM running for 788.118)
22:47:52.118 [Thread-43] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:47:52.122 [Thread-43] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-8} closing ...
22:47:52.127 [Thread-43] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-8} closed
22:47:52.418 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:47:52.418 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:47:52.707 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:47:52.707 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:47:52.707 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:47:52.709 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:47:52.920 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-9} inited
22:47:54.174 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8080"]
22:47:54.220 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 1.818 seconds (JVM running for 812.635)
22:48:08.247 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
22:50:04.308 [Thread-47] INFO  sys-user - [shutdownAsyncManager,31] - ====关闭后台任务任务线程池====
22:50:04.313 [Thread-47] INFO  c.a.d.p.DruidDataSource - [close,2204] - {dataSource-9} closing ...
22:50:04.316 [Thread-47] INFO  c.a.d.p.DruidDataSource - [close,2277] - {dataSource-9} closed
22:50:05.084 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 1.8.0_452 on mazihaodeMac-mini.local with PID 45301 (/Users/<USER>/Desktop/dev/performance/ruoyi-admin/target/classes started by mazihao in /Users/<USER>/Desktop/dev/performance)
22:50:05.084 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
22:50:05.549 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8080"]
22:50:05.549 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
22:50:05.549 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
22:50:05.552 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
22:50:05.817 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-10} inited
