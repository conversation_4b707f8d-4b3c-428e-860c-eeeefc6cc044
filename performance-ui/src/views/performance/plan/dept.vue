<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="科室名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入科室名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-input
          v-model="queryParams.taskType"
          placeholder="请输入任务类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['performance:plan:dept:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['performance:plan:dept:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleExportSelected" v-hasPermi="['performance:plan:dept:export']">导出Word</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload
          class="upload-demo"
          :action="importUrl"
          :headers="uploadHeaders"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          :show-file-list="false"
          style="display: inline-block;"
          v-hasPermi="['performance:plan:dept:import']">
          <el-button type="info" plain icon="el-icon-upload2" size="mini">导入Excel</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-download" size="mini" @click="handleDownloadTemplate" v-hasPermi="['performance:plan:dept:list']">下载模板</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading"
      :data="list" 
      @selection-change="handleSelectionChange" 
      ref="table" 
      :row-key="row => row.id" 
      :border="true" 
      style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="deptName" label="科室名称" width="120"/>
      <el-table-column prop="taskType" label="任务类型" width="120"/>
      <el-table-column prop="taskContent" label="绩效任务" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="targetMeasure" label="目标及措施" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="evalStandard" label="评价标准" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="scoreWeight" label="分值或权重" width="120"/>
      <el-table-column prop="principal" label="责任人" width="100"/>
      <el-table-column prop="deadline" label="完成时限" width="120"/>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['performance:plan:dept:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['performance:plan:dept:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="科室名称" prop="deptName">
              <el-input v-model="form.deptName" placeholder="请输入科室名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-input v-model="form.taskType" placeholder="请输入任务类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="绩效任务" prop="taskContent">
          <el-input v-model="form.taskContent" type="textarea" placeholder="请输入绩效任务" />
        </el-form-item>
        <el-form-item label="目标及措施" prop="targetMeasure">
          <el-input v-model="form.targetMeasure" type="textarea" placeholder="请输入目标及措施" />
        </el-form-item>
        <el-form-item label="评价标准" prop="evalStandard">
          <el-input v-model="form.evalStandard" type="textarea" placeholder="请输入评价标准" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分值或权重" prop="scoreWeight">
              <el-input v-model="form.scoreWeight" placeholder="请输入分值或权重" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="责任人" prop="principal">
              <el-input v-model="form.principal" placeholder="请输入责任人" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="完成时限" prop="deadline">
          <el-input v-model="form.deadline" placeholder="请输入完成时限" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listDept, getDept, addDept, updateDept, delDept, exportDept, batchExportDept, downloadExcelTemplate } from '@/api/performance/dept'

export default {
  name: "DeptPlan",
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      list: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deptName: null,
        taskType: null
      },
      form: {},
      rules: {
        deptName: [
          { required: true, message: "科室名称不能为空", trigger: "blur" },
          { max: 100, message: "科室名称长度不能超过100个字符", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "blur" },
          { max: 100, message: "任务类型长度不能超过100个字符", trigger: "blur" }
        ],
        taskContent: [
          { required: true, message: "绩效任务不能为空", trigger: "blur" },
          { max: 1000, message: "绩效任务长度不能超过1000个字符", trigger: "blur" }
        ],
        targetMeasure: [
          { max: 1000, message: "目标及措施长度不能超过1000个字符", trigger: "blur" }
        ],
        evalStandard: [
          { max: 1000, message: "评价标准长度不能超过1000个字符", trigger: "blur" }
        ],
        scoreWeight: [
          { max: 50, message: "分值或权重长度不能超过50个字符", trigger: "blur" }
        ],
        principal: [
          { max: 100, message: "责任人长度不能超过100个字符", trigger: "blur" }
        ],
        deadline: [
          { max: 100, message: "完成时限长度不能超过100个字符", trigger: "blur" }
        ]
      },
      importUrl: process.env.VUE_APP_BASE_API + "/performance/plan/dept/importExcel",
      uploadHeaders: {}
    }
  },
  created() {
    this.getList();
    // 设置上传认证头
    this.uploadHeaders = {
      Authorization: 'Bearer ' + this.$store.getters.token
    };
  },
  methods: {
    getList() {
      this.loading = true;
      listDept(this.queryParams).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        deptName: null,
        taskType: null,
        taskContent: null,
        targetMeasure: null,
        evalStandard: null,
        scoreWeight: null,
        principal: null,
        deadline: null
      };
      this.resetForm("form");
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加科室绩效计划";
    },
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDept(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改科室绩效计划";
      }).catch(() => {
        this.$modal.msgError("获取数据失败");
      });
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDept(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.$modal.msgError("修改失败");
            });
          } else {
            addDept(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.$modal.msgError("新增失败");
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const message = row
        ? `是否确认删除科室"${row.deptName}"的绩效计划数据项？`
        : `是否确认删除选中的${ids.length}条科室绩效计划数据项？`;

      this.$modal.confirm(message).then(function() {
        return delDept(ids.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要导出的数据");
        return;
      }
      // 如果只选择了一条数据，使用单个导出
      if (this.ids.length === 1) {
        const selectedRow = this.list.find(item => item.id === this.ids[0]);
        this.handleExportSingle(selectedRow);
        return;
      }
      // 多条数据使用批量导出
      this.$modal.loading("正在导出数据，请稍候...");
      batchExportDept(this.ids).then(response => {
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '科室绩效计划.docx';
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    handleExportSingle(row) {
      this.$modal.loading("正在导出数据，请稍候...");
      exportDept(row.id).then(response => {
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = `${row.deptName}_绩效计划.docx`;
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    handleImportSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess("导入成功");
        this.getList();
      } else {
        this.$modal.msgError(response.msg || "导入失败");
      }
    },
    handleImportError(error) {
      console.error("导入失败:", error);
      if (error.status === 401) {
        this.$modal.msgError("认证失败，请重新登录");
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login';
        });
      } else {
        this.$modal.msgError("导入失败，请检查文件格式");
      }
    },
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.$modal.msgError('只能上传Excel文件格式!');
      }
      return isExcel;
    },
    handleDownloadTemplate() {
      downloadExcelTemplate().then(response => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '科室绩效计划模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      });
    }
  }
}
</script> 

<style scoped>
.upload-demo {
  display: inline-block;
}
</style> 