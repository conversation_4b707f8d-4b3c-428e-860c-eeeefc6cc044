<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-input
          v-model="queryParams.taskType"
          placeholder="请输入任务类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['performance:leader:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['performance:leader:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" :disabled="multiple" @click="handleExportSelected" v-hasPermi="['performance:leader:export']">导出Word</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload
          class="upload-demo"
          :action="importUrl"
          :headers="uploadHeaders"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :before-upload="beforeImportUpload"
          :show-file-list="false"
          :data="importParams"
          style="display: inline-block;"
          v-hasPermi="['performance:leader:import']">
          <el-button type="info" plain icon="el-icon-upload2" size="mini">导入Excel</el-button>
        </el-upload>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-download" size="mini" @click="downloadTemplate" v-hasPermi="['performance:leader:list']">下载模板</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    
    <el-table 
      v-loading="loading"
      :data="list" 
      @selection-change="handleSelectionChange"
      style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="name" label="姓名" width="100"/>
      <el-table-column prop="taskType" label="任务类型" width="120"/>
      <el-table-column prop="performanceTask" label="绩效任务" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="targetMeasures" label="目标及措施" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="evaluationCriteria" label="评价标准" min-width="200" show-overflow-tooltip/>
      <el-table-column prop="taskCategory" label="责任分类" width="100"/>
      <el-table-column prop="weightScore" label="权重分值" width="100"/>
      <el-table-column prop="deadline" label="完成时限" width="120"/>
      <el-table-column label="操作" align="center" width="180" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['performance:leader:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['performance:leader:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="form.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-input v-model="form.taskType" placeholder="请输入任务类型/类别" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="绩效任务" prop="performanceTask">
          <el-input v-model="form.performanceTask" type="textarea" placeholder="请输入绩效任务" />
        </el-form-item>
        <el-form-item label="目标及措施" prop="targetMeasures">
          <el-input v-model="form.targetMeasures" type="textarea" placeholder="请输入目标及措施" />
        </el-form-item>
        <el-form-item label="评价标准" prop="evaluationCriteria">
          <el-input v-model="form.evaluationCriteria" type="textarea" placeholder="请输入评价标准" />
        </el-form-item>
        <el-row>
          <el-col :span="8">
            <el-form-item label="责任" prop="responsibility">
              <el-input v-model="form.responsibility" placeholder="请输入责任" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任分类" prop="taskCategory">
              <el-input v-model="form.taskCategory" placeholder="请输入责任分类" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="权重分值" prop="weightScore">
              <el-input v-model="form.weightScore" placeholder="请输入权重分值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="完成时限" prop="deadline">
              <el-input v-model="form.deadline" placeholder="请输入完成时限" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属组织" prop="orgName">
              <el-input v-model="form.orgName" placeholder="请输入所属组织" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="计划年份" prop="planYear">
              <el-input v-model="form.planYear" placeholder="请输入计划年份" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listLeader, getLeader, addLeader, updateLeader, delLeader,
  exportLeader, batchExportLeader, downloadTemplate, downloadExcelTemplate
} from '@/api/performance/leader'

export default {
  name: "LeaderPlan",
  data() {
    return {
      loading: true,
      ids: [],
      single: true,
      multiple: true,
      showSearch: true,
      total: 0,
      list: [],
      title: "",
      open: false,
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        taskType: null,
        orgName: null,
        planYear: null
      },
      form: {},
      rules: {
        name: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
          { max: 100, message: "姓名长度不能超过100个字符", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "blur" },
          { max: 100, message: "任务类型长度不能超过100个字符", trigger: "blur" }
        ],
        performanceTask: [
          { required: true, message: "绩效任务不能为空", trigger: "blur" },
          { max: 1000, message: "绩效任务长度不能超过1000个字符", trigger: "blur" }
        ],
        targetMeasures: [
          { max: 1000, message: "目标及措施长度不能超过1000个字符", trigger: "blur" }
        ],
        evaluationCriteria: [
          { max: 1000, message: "评价标准长度不能超过1000个字符", trigger: "blur" }
        ],
        responsibility: [
          { max: 100, message: "责任长度不能超过100个字符", trigger: "blur" }
        ],
        taskCategory: [
          { max: 100, message: "责任分类长度不能超过100个字符", trigger: "blur" }
        ],
        weightScore: [
          { max: 50, message: "权重分值长度不能超过50个字符", trigger: "blur" }
        ],
        deadline: [
          { max: 100, message: "完成时限长度不能超过100个字符", trigger: "blur" }
        ],
        orgName: [
          { max: 100, message: "所属组织长度不能超过100个字符", trigger: "blur" }
        ],
        planYear: [
          { max: 10, message: "计划年份长度不能超过10个字符", trigger: "blur" }
        ]
      },
      importUrl: process.env.VUE_APP_BASE_API + '/performance/leader/importExcel',
      importParams: { planYear: '', orgName: '' },
      uploadHeaders: {}
    }
  },
  created() {
    this.getList();
    // 设置上传认证头
    this.uploadHeaders = {
      Authorization: 'Bearer ' + this.$store.getters.token
    };
  },
  methods: {
    getList() {
      this.loading = true;
      listLeader(this.queryParams).then(response => {
        this.list = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        id: null,
        name: null,
        taskType: null,
        performanceTask: null,
        targetMeasures: null,
        evaluationCriteria: null,
        responsibility: null,
        taskCategory: null,
        weightScore: null,
        deadline: null,
        orgName: null,
        planYear: null
      };
      this.resetForm("form");
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加班子成员绩效计划";
    },
    handleEdit(row) {
      this.reset();
      const id = row.id;
      getLeader(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改班子成员绩效计划";
      }).catch(() => {
        this.$modal.msgError("获取数据失败");
      });
    },
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLeader(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.$modal.msgError("修改失败");
            });
          } else {
            addLeader(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(() => {
              this.$modal.msgError("新增失败");
            });
          }
        }
      });
    },
    handleDelete(row) {
      const ids = row ? [row.id] : this.ids;
      const message = row
        ? `是否确认删除"${row.name}"的绩效计划数据项？`
        : `是否确认删除选中的${ids.length}条班子成员绩效计划数据项？`;

      this.$modal.confirm(message).then(function() {
        return delLeader(ids.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleExportSelected() {
      if (this.ids.length === 0) {
        this.$modal.msgError("请选择要导出的数据");
        return;
      }
      // 如果只选择了一条数据，使用单个导出
      if (this.ids.length === 1) {
        const selectedRow = this.list.find(item => item.id === this.ids[0]);
        this.handleExportSingle(selectedRow);
        return;
      }
      // 多条数据使用批量导出
      this.$modal.loading("正在导出数据，请稍候...");
      batchExportLeader(this.ids).then(response => {
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '班子成员绩效计划.docx';
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    handleExportSingle(row) {
      this.$modal.loading("正在导出数据，请稍候...");
      exportLeader(row.id).then(response => {
        const blob = new Blob([response], { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = `${row.name || '班子成员'}_绩效计划.docx`;
        link.click();
        window.URL.revokeObjectURL(link.href);
        this.$modal.closeLoading();
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    beforeImportUpload(file) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                      file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        this.$modal.msgError('只能上传Excel文件格式(.xlsx或.xls)!');
      }
      return isExcel;
    },
    handleImportSuccess(response) {
      if (response.code === 200) {
        this.$modal.msgSuccess('导入成功');
        this.getList();
      } else {
        this.$modal.msgError(response.msg || '导入失败');
      }
    },
    handleImportError(error) {
      console.error("导入失败:", error);
      if (error.status === 401) {
        this.$modal.msgError("认证失败，请重新登录");
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/login';
        });
      } else {
        this.$modal.msgError("导入失败，请检查文件格式");
      }
    },
    downloadTemplate() {
      downloadExcelTemplate().then(response => {
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = '班子成员绩效计划模板.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      });
    }
  }
}
</script> 

<style scoped>
.upload-demo {
  display: inline-block;
  }
  </style> 