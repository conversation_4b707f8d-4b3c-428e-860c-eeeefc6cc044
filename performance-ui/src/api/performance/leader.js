import request from '@/utils/request'

// 查询领导班子绩效计划列表
export function listLeader(query) {
  return request({
    url: '/performance/leader/list',
    method: 'get',
    params: query
  })
}

// 查询领导班子绩效计划详细信息
export function getLeader(id) {
  return request({
    url: '/performance/leader/' + id,
    method: 'get'
  })
}

// 新增领导班子绩效计划
export function addLeader(data) {
  return request({
    url: '/performance/leader',
    method: 'post',
    data: data
  })
}

// 修改领导班子绩效计划
export function updateLeader(data) {
  return request({
    url: '/performance/leader',
    method: 'put',
    data: data
  })
}

// 删除领导班子绩效计划
export function delLeader(ids) {
  return request({
    url: '/performance/leader/' + ids,
    method: 'delete'
  })
}

// 导出单个Word文档
export function exportLeader(id) {
  return request({
    url: '/performance/leader/export/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导出Word文档
export function batchExportLeader(ids) {
  return request({
    url: '/performance/leader/batchExport',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

// 导入Word文档
export function importLeader(data) {
  return request({
    url: '/performance/leader/import',
    method: 'post',
    data: data
  })
}

// 导入Excel文档
export function importExcelLeader(data) {
  return request({
    url: '/performance/leader/importExcel',
    method: 'post',
    data: data
  })
}

// 下载模板
export function downloadTemplate() {
  return request({
    url: '/performance/leader/downloadTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

// 下载Excel模板
export function downloadExcelTemplate() {
  return request({
    url: '/performance/leader/downloadExcelTemplate',
    method: 'get',
    responseType: 'blob'
  })
}