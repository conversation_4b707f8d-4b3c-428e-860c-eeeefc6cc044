import request from '@/utils/request'

// 查询科室绩效计划列表
export function listDept(query) {
  return request({
    url: '/performance/plan/dept/list',
    method: 'get',
    params: query
  })
}

// 查询科室绩效计划详细信息
export function getDept(id) {
  return request({
    url: '/performance/plan/dept/' + id,
    method: 'get'
  })
}

// 新增科室绩效计划
export function addDept(data) {
  return request({
    url: '/performance/plan/dept',
    method: 'post',
    data: data
  })
}

// 修改科室绩效计划
export function updateDept(data) {
  return request({
    url: '/performance/plan/dept',
    method: 'put',
    data: data
  })
}

// 删除科室绩效计划
export function delDept(ids) {
  return request({
    url: '/performance/plan/dept/' + ids,
    method: 'delete'
  })
}

// 导出单个Word文档
export function exportDept(id) {
  return request({
    url: '/performance/plan/dept/export/' + id,
    method: 'get',
    responseType: 'blob'
  })
}

// 批量导出Word文档
export function batchExportDept(ids) {
  return request({
    url: '/performance/plan/dept/batchExport',
    method: 'post',
    data: ids,
    responseType: 'blob'
  })
}

// 下载Word模板
export function downloadTemplate() {
  return request({
    url: '/performance/plan/dept/downloadTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

// 导入Excel文档
export function importExcel(data) {
  return request({
    url: '/performance/plan/dept/importExcel',
    method: 'post',
    data: data
  })
}

// 下载Excel模板
export function downloadExcelTemplate() {
  return request({
    url: '/performance/plan/dept/downloadExcelTemplate',
    method: 'get',
    responseType: 'blob'
  })
}