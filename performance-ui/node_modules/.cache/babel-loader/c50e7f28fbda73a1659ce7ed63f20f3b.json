{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js!/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelYearly.js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelYearly.js", "mtime": 1754401920785}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/babel.config.js", "mtime": 1753510681759}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCIvVXNlcnMvbWF6aWhhby9EZXNrdG9wL2Rldi9wZXJmb3JtYW5jZS9wZXJmb3JtYW5jZS11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkUGVyc29ubmVsWWVhcmx5UGxhbiA9IGFkZFBlcnNvbm5lbFllYXJseVBsYW47CmV4cG9ydHMuZGVsUGVyc29ubmVsWWVhcmx5UGxhbiA9IGRlbFBlcnNvbm5lbFllYXJseVBsYW47CmV4cG9ydHMuZG93bmxvYWRZZWFybHlFeGNlbFRlbXBsYXRlID0gZG93bmxvYWRZZWFybHlFeGNlbFRlbXBsYXRlOwpleHBvcnRzLmdldENvbW1vbkluZGljYXRvcnMgPSBnZXRDb21tb25JbmRpY2F0b3JzOwpleHBvcnRzLmdldFBlcnNvbmFsSW5kaWNhdG9ycyA9IGdldFBlcnNvbmFsSW5kaWNhdG9yczsKZXhwb3J0cy5nZXRQZXJzb25uZWxZZWFybHlQbGFuID0gZ2V0UGVyc29ubmVsWWVhcmx5UGxhbjsKZXhwb3J0cy5pbXBvcnRZZWFybHlFeGNlbCA9IGltcG9ydFllYXJseUV4Y2VsOwpleHBvcnRzLmxpc3RQZXJzb25uZWxZZWFybHlQbGFuID0gbGlzdFBlcnNvbm5lbFllYXJseVBsYW47CmV4cG9ydHMudXBkYXRlUGVyc29ubmVsWWVhcmx5UGxhbiA9IHVwZGF0ZVBlcnNvbm5lbFllYXJseVBsYW47CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDmn6Xor6LkuKrkurrlubTluqbnu6nmlYjorqHliJLliJfooagKZnVuY3Rpb24gbGlzdFBlcnNvbm5lbFllYXJseVBsYW4ocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwveWVhcmx5L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5Liq5Lq65bm05bqm57up5pWI6K6h5YiS6K+m57uG5L+h5oGvCmZ1bmN0aW9uIGdldFBlcnNvbm5lbFllYXJseVBsYW4oaWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwveWVhcmx5LycgKyBpZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5Liq5Lq65bm05bqm57up5pWI6K6h5YiSCmZ1bmN0aW9uIGFkZFBlcnNvbm5lbFllYXJseVBsYW4oZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC95ZWFybHknLAogICAgbWV0aG9kOiAncG9zdCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOS/ruaUueS4quS6uuW5tOW6pue7qeaViOiuoeWIkgpmdW5jdGlvbiB1cGRhdGVQZXJzb25uZWxZZWFybHlQbGFuKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwveWVhcmx5JywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOS4quS6uuW5tOW6pue7qeaViOiuoeWIkgpmdW5jdGlvbiBkZWxQZXJzb25uZWxZZWFybHlQbGFuKGlkcykgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC95ZWFybHkvJyArIGlkcywKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g6I635Y+W5Liq5oCn5oyH5qCH5YiX6KGoCmZ1bmN0aW9uIGdldFBlcnNvbmFsSW5kaWNhdG9ycyhwbGFuSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwveWVhcmx5LycgKyBwbGFuSWQgKyAnL3BlcnNvbmFsSW5kaWNhdG9ycycsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOiOt+WPluWFseaAp+aMh+agh+WIl+ihqApmdW5jdGlvbiBnZXRDb21tb25JbmRpY2F0b3JzKHBsYW5JZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3BlcmZvcm1hbmNlL3BlcnNvbm5lbC95ZWFybHkvJyArIHBsYW5JZCArICcvY29tbW9uSW5kaWNhdG9ycycsCiAgICBtZXRob2Q6ICdnZXQnCiAgfSk7Cn0KCi8vIOWvvOWFpUV4Y2Vs5paH5qGjCmZ1bmN0aW9uIGltcG9ydFllYXJseUV4Y2VsKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9wZXJmb3JtYW5jZS9wZXJzb25uZWwveWVhcmx5L2ltcG9ydEV4Y2VsJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkuIvovb1FeGNlbOaooeadvwpmdW5jdGlvbiBkb3dubG9hZFllYXJseUV4Y2VsVGVtcGxhdGUoKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvcGVyZm9ybWFuY2UvcGVyc29ubmVsL3llYXJseS9kb3dubG9hZEV4Y2VsVGVtcGxhdGUnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHJlc3BvbnNlVHlwZTogJ2Jsb2InCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPersonnelYearlyPlan", "query", "request", "url", "method", "params", "getPersonnelYearlyPlan", "id", "addPersonnelYearlyPlan", "data", "updatePersonnelYearlyPlan", "delPersonnelYearlyPlan", "ids", "getPersonalIndicators", "planId", "getCommonIndicators", "importYearlyExcel", "downloadYearlyExcelTemplate", "responseType"], "sources": ["/Users/<USER>/Desktop/dev/performance/performance-ui/src/api/performance/personnelYearly.js"], "sourcesContent": ["import request from '@/utils/request'\n\n// 查询个人年度绩效计划列表\nexport function listPersonnelYearlyPlan(query) {\n  return request({\n    url: '/performance/personnel/yearly/list',\n    method: 'get',\n    params: query\n  })\n}\n\n// 查询个人年度绩效计划详细信息\nexport function getPersonnelYearlyPlan(id) {\n  return request({\n    url: '/performance/personnel/yearly/' + id,\n    method: 'get'\n  })\n}\n\n// 新增个人年度绩效计划\nexport function addPersonnelYearlyPlan(data) {\n  return request({\n    url: '/performance/personnel/yearly',\n    method: 'post',\n    data: data\n  })\n}\n\n// 修改个人年度绩效计划\nexport function updatePersonnelYearlyPlan(data) {\n  return request({\n    url: '/performance/personnel/yearly',\n    method: 'put',\n    data: data\n  })\n}\n\n// 删除个人年度绩效计划\nexport function delPersonnelYearlyPlan(ids) {\n  return request({\n    url: '/performance/personnel/yearly/' + ids,\n    method: 'delete'\n  })\n}\n\n// 获取个性指标列表\nexport function getPersonalIndicators(planId) {\n  return request({\n    url: '/performance/personnel/yearly/' + planId + '/personalIndicators',\n    method: 'get'\n  })\n}\n\n// 获取共性指标列表\nexport function getCommonIndicators(planId) {\n  return request({\n    url: '/performance/personnel/yearly/' + planId + '/commonIndicators',\n    method: 'get'\n  })\n}\n\n// 导入Excel文档\nexport function importYearlyExcel(data) {\n  return request({\n    url: '/performance/personnel/yearly/importExcel',\n    method: 'post',\n    data: data\n  })\n}\n\n// 下载Excel模板\nexport function downloadYearlyExcelTemplate() {\n  return request({\n    url: '/performance/personnel/yearly/downloadExcelTemplate',\n    method: 'get',\n    responseType: 'blob'\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,uBAAuBA,CAACC,KAAK,EAAE;EAC7C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oCAAoC;IACzCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,sBAAsBA,CAACC,EAAE,EAAE;EACzC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGI,EAAE;IAC1CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,yBAAyBA,CAACD,IAAI,EAAE;EAC9C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,sBAAsBA,CAACC,GAAG,EAAE;EAC1C,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGS,GAAG;IAC3CR,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGW,MAAM,GAAG,qBAAqB;IACtEV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,mBAAmBA,CAACD,MAAM,EAAE;EAC1C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC,GAAGW,MAAM,GAAG,mBAAmB;IACpEV,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,iBAAiBA,CAACP,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2CAA2C;IAChDC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,2BAA2BA,CAAA,EAAG;EAC5C,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,qDAAqD;IAC1DC,MAAM,EAAE,KAAK;IACbc,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}