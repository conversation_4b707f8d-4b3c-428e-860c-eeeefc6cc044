{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/dept.vue?vue&type=style&index=0&id=195312a2&scoped=true&lang=css", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/dept.vue", "mtime": 1754317402895}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/css-loader/dist/cjs.js", "mtime": 1753510683027}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/stylePostLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/postcss-loader/src/index.js", "mtime": 1753510684004}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCi51cGxvYWQtZGVtbyB7CiAgZGlzcGxheTogaW5saW5lLWJsb2NrOwp9Cg=="}, {"version": 3, "sources": ["dept.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoYA;AACA;AACA", "file": "dept.vue", "sourceRoot": "src/views/performance/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\n      <el-form-item label=\"科室名称\" prop=\"deptName\">\n        <el-input\n          v-model=\"queryParams.deptName\"\n          placeholder=\"请输入科室名称\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item label=\"任务类型\" prop=\"taskType\">\n        <el-input\n          v-model=\"queryParams.taskType\"\n          placeholder=\"请输入任务类型\"\n          clearable\n          @keyup.enter.native=\"handleQuery\"\n        />\n      </el-form-item>\n      <el-form-item>\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n      </el-form-item>\n    </el-form>\n\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\" v-hasPermi=\"['performance:plan:dept:add']\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"danger\" plain icon=\"el-icon-delete\" size=\"mini\" :disabled=\"multiple\" @click=\"handleDelete\" v-hasPermi=\"['performance:plan:dept:remove']\">删除</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\" v-hasPermi=\"['performance:plan:dept:export']\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          style=\"display: inline-block;\"\n          v-hasPermi=\"['performance:plan:dept:import']\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\" v-hasPermi=\"['performance:plan:dept:list']\">下载模板</el-button>\n      </el-col>\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n    </el-row>\n\n    <el-table \n      v-loading=\"loading\"\n      :data=\"list\" \n      @selection-change=\"handleSelectionChange\" \n      ref=\"table\" \n      :row-key=\"row => row.id\" \n      :border=\"true\" \n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column prop=\"deptName\" label=\"科室名称\" width=\"120\"/>\n      <el-table-column prop=\"taskType\" label=\"任务类型\" width=\"120\"/>\n      <el-table-column prop=\"taskContent\" label=\"绩效任务\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"targetMeasure\" label=\"目标及措施\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"evalStandard\" label=\"评价标准\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column prop=\"scoreWeight\" label=\"分值或权重\" width=\"120\"/>\n      <el-table-column prop=\"principal\" label=\"责任人\" width=\"100\"/>\n      <el-table-column prop=\"deadline\" label=\"完成时限\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" width=\"180\" class-name=\"small-padding fixed-width\">\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['performance:plan:dept:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"mini\"\n            type=\"text\"\n            icon=\"el-icon-delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:plan:dept:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"800px\" append-to-body>\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"科室名称\" prop=\"deptName\">\n              <el-input v-model=\"form.deptName\" placeholder=\"请输入科室名称\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"任务类型\" prop=\"taskType\">\n              <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"绩效任务\" prop=\"taskContent\">\n          <el-input v-model=\"form.taskContent\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasure\">\n          <el-input v-model=\"form.targetMeasure\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"评价标准\" prop=\"evalStandard\">\n          <el-input v-model=\"form.evalStandard\" type=\"textarea\" placeholder=\"请输入评价标准\" />\n        </el-form-item>\n        <el-row>\n          <el-col :span=\"12\">\n            <el-form-item label=\"分值或权重\" prop=\"scoreWeight\">\n              <el-input v-model=\"form.scoreWeight\" placeholder=\"请输入分值或权重\" />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"12\">\n            <el-form-item label=\"责任人\" prop=\"principal\">\n              <el-input v-model=\"form.principal\" placeholder=\"请输入责任人\" />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-form-item label=\"完成时限\" prop=\"deadline\">\n          <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n        <el-button @click=\"cancel\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n<script>\nimport { listDept, getDept, addDept, updateDept, delDept, exportDept, batchExportDept, downloadExcelTemplate } from '@/api/performance/dept'\n\nexport default {\n  name: \"DeptPlan\",\n  data() {\n    return {\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      total: 0,\n      list: [],\n      title: \"\",\n      open: false,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        deptName: null,\n        taskType: null\n      },\n      form: {},\n      rules: {\n        deptName: [\n          { required: true, message: \"科室名称不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"科室名称长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" },\n          { max: 100, message: \"任务类型长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        taskContent: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" },\n          { max: 1000, message: \"绩效任务长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        targetMeasure: [\n          { max: 1000, message: \"目标及措施长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        evalStandard: [\n          { max: 1000, message: \"评价标准长度不能超过1000个字符\", trigger: \"blur\" }\n        ],\n        scoreWeight: [\n          { max: 50, message: \"分值或权重长度不能超过50个字符\", trigger: \"blur\" }\n        ],\n        principal: [\n          { max: 100, message: \"责任人长度不能超过100个字符\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { max: 100, message: \"完成时限长度不能超过100个字符\", trigger: \"blur\" }\n        ]\n      },\n      importUrl: process.env.VUE_APP_BASE_API + \"/performance/plan/dept/importExcel\",\n      uploadHeaders: {}\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true;\n      listDept(this.queryParams).then(response => {\n        this.list = response.rows;\n        this.total = response.total;\n        this.loading = false;\n      });\n    },\n    handleQuery() {\n      this.queryParams.pageNum = 1;\n      this.getList();\n    },\n    resetQuery() {\n      this.resetForm(\"queryForm\");\n      this.handleQuery();\n    },\n    cancel() {\n      this.open = false;\n      this.reset();\n    },\n    reset() {\n      this.form = {\n        id: null,\n        deptName: null,\n        taskType: null,\n        taskContent: null,\n        targetMeasure: null,\n        evalStandard: null,\n        scoreWeight: null,\n        principal: null,\n        deadline: null\n      };\n      this.resetForm(\"form\");\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length!==1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.reset();\n      this.open = true;\n      this.title = \"添加科室绩效计划\";\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids\n      getDept(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改科室绩效计划\";\n      }).catch(() => {\n        this.$modal.msgError(\"获取数据失败\");\n      });\n    },\n    submitForm() {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          if (this.form.id != null) {\n            updateDept(this.form).then(() => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"修改失败\");\n            });\n          } else {\n            addDept(this.form).then(() => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            }).catch(() => {\n              this.$modal.msgError(\"新增失败\");\n            });\n          }\n        }\n      });\n    },\n    handleDelete(row) {\n      const ids = row ? [row.id] : this.ids;\n      const message = row\n        ? `是否确认删除科室\"${row.deptName}\"的绩效计划数据项？`\n        : `是否确认删除选中的${ids.length}条科室绩效计划数据项？`;\n\n      this.$modal.confirm(message).then(function() {\n        return delDept(ids.join(','));\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgError(\"请选择要导出的数据\");\n        return;\n      }\n      // 如果只选择了一条数据，使用单个导出\n      if (this.ids.length === 1) {\n        const selectedRow = this.list.find(item => item.id === this.ids[0]);\n        this.handleExportSingle(selectedRow);\n        return;\n      }\n      // 多条数据使用批量导出\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      batchExportDept(this.ids).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '科室绩效计划.docx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleExportSingle(row) {\n      this.$modal.loading(\"正在导出数据，请稍候...\");\n      exportDept(row.id).then(response => {\n        const blob = new Blob([response], { \n          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' \n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = `${row.deptName}_绩效计划.docx`;\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleImportSuccess(response) {\n      if (response.code === 200) {\n        this.$modal.msgSuccess(\"导入成功\");\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || \"导入失败\");\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                      file.type === 'application/vnd.ms-excel';\n      if (!isExcel) {\n        this.$modal.msgError('只能上传Excel文件格式!');\n      }\n      return isExcel;\n    },\n    handleDownloadTemplate() {\n      downloadExcelTemplate().then(response => {\n        const blob = new Blob([response], {\n          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '科室绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n      });\n    }\n  }\n}\n</script> \n\n<style scoped>\n.upload-demo {\n  display: inline-block;\n}\n</style> "]}]}