{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue?vue&type=template&id=ee50d736&scoped=true", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/leader/index.vue", "mtime": 1754318751675}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/loaders/templateLoader.js", "mtime": 1753510684373}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}