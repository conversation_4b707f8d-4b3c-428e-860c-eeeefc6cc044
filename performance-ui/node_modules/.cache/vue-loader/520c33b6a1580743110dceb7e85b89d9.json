{"remainingRequest": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js??vue-loader-options!/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue?vue&type=script&lang=js", "dependencies": [{"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/src/views/performance/plan/index.vue", "mtime": 1754316351470}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/babel-loader/lib/index.js", "mtime": 1753510682448}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/cache-loader/dist/cjs.js", "mtime": 1753510682629}, {"path": "/Users/<USER>/Desktop/dev/performance/performance-ui/node_modules/vue-loader/lib/index.js", "mtime": 1753510684373}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6GA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/performance/plan", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"10\" class=\"mb8\">\n      <el-col :span=\"1.5\">\n        <el-button type=\"primary\" plain icon=\"el-icon-plus\" size=\"mini\" @click=\"handleAdd\">新增</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"warning\" plain icon=\"el-icon-download\" size=\"mini\" :disabled=\"multiple\" @click=\"handleExportSelected\">导出Word</el-button>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-upload\n          class=\"upload-demo\"\n          :action=\"importUrl\"\n          :headers=\"uploadHeaders\"\n          :on-success=\"handleImportSuccess\"\n          :on-error=\"handleImportError\"\n          :before-upload=\"beforeImportUpload\"\n          :show-file-list=\"false\"\n          style=\"display: inline-block;\">\n          <el-button type=\"info\" plain icon=\"el-icon-upload2\" size=\"mini\">导入Excel</el-button>\n        </el-upload>\n      </el-col>\n      <el-col :span=\"1.5\">\n        <el-button type=\"info\" plain icon=\"el-icon-download\" size=\"mini\" @click=\"handleDownloadTemplate\">下载模板</el-button>\n      </el-col>\n    </el-row>\n\n    <el-table\n      v-loading=\"loading\"\n      :data=\"planList\"\n      @selection-change=\"handleSelectionChange\"\n      row-key=\"id\"\n      border\n      style=\"width: 100%\">\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n      <el-table-column label=\"任务类型\" align=\"center\" prop=\"taskType\" width=\"120\"/>\n      <el-table-column label=\"任务来源\" align=\"center\" prop=\"taskSource\" width=\"120\"/>\n      <el-table-column label=\"绩效任务\" align=\"center\" prop=\"performanceTask\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"目标及措施\" align=\"center\" prop=\"targetMeasures\" min-width=\"200\" show-overflow-tooltip/>\n      <el-table-column label=\"责任科室\" align=\"center\" prop=\"responsibleDept\" width=\"120\"/>\n      <el-table-column label=\"分值及权重\" align=\"center\" prop=\"valueWeight\" width=\"120\"/>\n      <el-table-column label=\"责任领导\" align=\"center\" prop=\"responsibleLeader\" width=\"120\"/>\n      <el-table-column label=\"完成时限\" align=\"center\" prop=\"deadline\" width=\"120\"/>\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\n        <template #default=\"scope\">\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Edit\"\n            @click=\"handleUpdate(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:edit']\"\n          >修改</el-button>\n          <el-button\n            size=\"small\"\n            type=\"text\"\n            icon=\"Delete\"\n            @click=\"handleDelete(scope.row)\"\n            v-hasPermi=\"['performance:plan:org:remove']\"\n          >删除</el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <pagination\n      v-show=\"total>0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getList\"\n    />\n\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"780px\" append-to-body>\n      <el-form ref=\"planRef\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\n        <el-form-item label=\"任务类型\" prop=\"taskType\">\n          <el-input v-model=\"form.taskType\" placeholder=\"请输入任务类型\" />\n        </el-form-item>\n        <el-form-item label=\"任务来源\" prop=\"taskSource\">\n          <el-input v-model=\"form.taskSource\" placeholder=\"请输入任务来源\" />\n        </el-form-item>\n        <el-form-item label=\"绩效任务\" prop=\"performanceTask\">\n          <el-input v-model=\"form.performanceTask\" type=\"textarea\" placeholder=\"请输入绩效任务\" />\n        </el-form-item>\n        <el-form-item label=\"目标及措施\" prop=\"targetMeasures\">\n          <el-input v-model=\"form.targetMeasures\" type=\"textarea\" placeholder=\"请输入目标及措施\" />\n        </el-form-item>\n        <el-form-item label=\"责任科室\" prop=\"responsibleDept\">\n          <el-input v-model=\"form.responsibleDept\" placeholder=\"请输入责任科室\" />\n        </el-form-item>\n        <el-form-item label=\"分值及权重\" prop=\"valueWeight\">\n          <el-input v-model=\"form.valueWeight\" placeholder=\"请输入分值及权重\" />\n        </el-form-item>\n        <el-form-item label=\"责任领导\" prop=\"responsibleLeader\">\n          <el-input v-model=\"form.responsibleLeader\" placeholder=\"请输入责任领导\" />\n        </el-form-item>\n        <el-form-item label=\"完成时限\" prop=\"deadline\">\n          <el-input v-model=\"form.deadline\" placeholder=\"请输入完成时限\" />\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\n          <el-button @click=\"cancel\">取 消</el-button>\n        </div>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { listOrgPlan, getOrgPlan, addOrgPlan, updateOrgPlan, delOrgPlan } from '@/api/performance/org'\nimport { listPlan, getPlan, addPlan, updatePlan, delPlan, exportOrgPlan, batchExportOrgPlan, downloadOrgExcelTemplate } from \"@/api/performance/plan\"\n\nexport default {\n  name: 'OrgPlan',\n  data() {\n    return {\n      planList: [],\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10\n      },\n      formVisible: false,\n      formTitle: '',\n      form: {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      },\n      loading: true,\n      ids: [],\n      single: true,\n      multiple: true,\n      showSearch: true,\n      title: '',\n      open: false,\n      importUrl: process.env.VUE_APP_BASE_API + '/performance/plan/org/importExcel',\n      uploadHeaders: {},\n      rules: {\n        taskType: [\n          { required: true, message: \"任务类型不能为空\", trigger: \"blur\" }\n        ],\n        taskSource: [\n          { required: true, message: \"任务来源不能为空\", trigger: \"blur\" }\n        ],\n        performanceTask: [\n          { required: true, message: \"绩效任务不能为空\", trigger: \"blur\" }\n        ],\n        targetMeasures: [\n          { required: true, message: \"目标及措施不能为空\", trigger: \"blur\" }\n        ],\n        responsibleDept: [\n          { required: true, message: \"责任科室不能为空\", trigger: \"blur\" }\n        ],\n        valueWeight: [\n          { required: true, message: \"分值及权重不能为空\", trigger: \"blur\" }\n        ],\n        responsibleLeader: [\n          { required: true, message: \"责任领导不能为空\", trigger: \"blur\" }\n        ],\n        deadline: [\n          { required: true, message: \"完成时限不能为空\", trigger: \"blur\" }\n        ]\n      }\n    }\n  },\n  created() {\n    this.getList();\n    // 设置上传认证头\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    };\n  },\n  methods: {\n    getList() {\n      this.loading = true\n      listOrgPlan(this.queryParams).then(res => {\n        this.planList = res.rows || res\n        this.total = res.total || (res.length || 0)\n        this.loading = false\n      })\n    },\n    handleSelectionChange(selection) {\n      this.ids = selection.map(item => item.id)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n    },\n    handleAdd() {\n      this.title = '新增绩效计划'\n      this.form = { id: undefined, seq: '', taskType: '', taskSource: '', performanceTask: '', targetMeasures: '', responsibleDept: '', valueWeight: '', responsibleLeader: '', deadline: '' }\n      this.open = true\n    },\n    handleEdit(row) {\n      this.title = '编辑绩效计划'\n      this.form = Object.assign({}, row)\n      this.open = true\n    },\n    handleDelete(row) {\n      const ids = row.id || this.ids;\n      this.$modal.confirm('是否确认删除绩效计划编号为\"' + ids + '\"的数据项？').then(function() {\n        return delOrgPlan(ids);\n      }).then(() => {\n        this.getList();\n        this.$modal.msgSuccess(\"删除成功\");\n      }).catch(() => {});\n    },\n    handleExportSelected() {\n      if (this.ids.length === 0) {\n        this.$modal.msgWarning(\"请选择要导出的数据\");\n        return;\n      }\n      this.$modal.confirm('是否确认导出选中的' + this.ids.length + '条绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        batchExportOrgPlan(this.ids).then(response => {\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划_' + new Date().getTime() + '.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n          this.$modal.msgSuccess(\"导出成功\");\n        }).catch(() => {\n          this.$modal.closeLoading();\n          this.$modal.msgError(\"导出失败\");\n        });\n      }).catch(() => {});\n    },\n    handleExport() {\n      this.$modal.confirm('是否确认导出所有绩效计划数据项？').then(() => {\n        this.$modal.loading(\"正在导出数据，请稍候...\");\n        exportOrgPlan().then(response => {\n          const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });\n          const link = document.createElement('a');\n          link.href = window.URL.createObjectURL(blob);\n          link.download = '组织绩效计划.docx';\n          link.click();\n          window.URL.revokeObjectURL(link.href);\n          this.$modal.closeLoading();\n        }).catch(() => {\n          this.$modal.closeLoading();\n        });\n      }).catch(() => {});\n    },\n    handleDownloadTemplate() {\n      this.$modal.loading(\"正在下载模板，请稍候...\");\n      downloadOrgExcelTemplate().then(response => {\n        const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = '组织绩效计划模板.xlsx';\n        link.click();\n        window.URL.revokeObjectURL(link.href);\n        this.$modal.closeLoading();\n      }).catch(() => {\n        this.$modal.closeLoading();\n      });\n    },\n    handleUpdate(row) {\n      this.reset();\n      const id = row.id || this.ids;\n      getPlan(id).then(response => {\n        this.form = response.data;\n        this.open = true;\n        this.title = \"修改绩效计划\";\n      });\n    },\n    submitForm() {\n      this.$refs[\"planRef\"].validate(valid => {\n        if (valid) {\n          if (this.form.id) {\n            updatePlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\");\n              this.open = false;\n              this.getList();\n            });\n          } else {\n            addPlan(this.form).then(response => {\n              this.$modal.msgSuccess(\"新增成功\");\n              this.open = false;\n              this.getList();\n            });\n          }\n        }\n      });\n    },\n    handleImportSuccess(response, file, fileList) {\n      if (response.code === 200 || response === '导入成功') {\n        this.$modal.msgSuccess('导入成功');\n        this.getList();\n      } else {\n        this.$modal.msgError(response.msg || '导入失败');\n      }\n    },\n    handleImportError(error) {\n      console.error(\"导入失败:\", error);\n      if (error.status === 401) {\n        this.$modal.msgError(\"认证失败，请重新登录\");\n        this.$store.dispatch('LogOut').then(() => {\n          location.href = '/login';\n        });\n      } else {\n        this.$modal.msgError(\"导入失败，请检查文件格式\");\n      }\n    },\n    beforeImportUpload(file) {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                     file.type === 'application/vnd.ms-excel'\n      if (!isExcel) {\n        this.$message.error('只能上传Excel文件!')\n      }\n      return isExcel\n    },\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    reset() {\n      this.form = {\n        id: undefined,\n        seq: '',\n        taskType: '',\n        taskSource: '',\n        performanceTask: '',\n        targetMeasures: '',\n        responsibleDept: '',\n        valueWeight: '',\n        responsibleLeader: '',\n        deadline: ''\n      }\n      this.resetForm(\"planRef\")\n    },\n    formatJson(filterVal, jsonData) {\n      return jsonData.map(v => filterVal.map(j => v[j]))\n    }\n  }\n}\n</script>\n\n<style scoped>\n.mb8 {\n  margin-bottom: 8px;\n}\n.upload-demo {\n  display: inline-block;\n}\n</style>\n"]}]}