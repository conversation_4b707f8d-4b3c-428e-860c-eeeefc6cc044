package com.ruoyi.system.domain;

import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 个人年度绩效计划个性指标表
 */
public class PersonnelYearlyPersonalIndicator extends BaseEntity {
    private Long id;
    private Long planId;
    private Integer seqNo;
    private String taskCategory;
    private String performanceTask;
    private String targetMeasures;
    private String evaluationCriteria;
    private String scoreWeight;
    private String completionTime;
    private Date createTime;
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Integer getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(Integer seqNo) {
        this.seqNo = seqNo;
    }

    public String getTaskCategory() {
        return taskCategory;
    }

    public void setTaskCategory(String taskCategory) {
        this.taskCategory = taskCategory;
    }

    public String getPerformanceTask() {
        return performanceTask;
    }

    public void setPerformanceTask(String performanceTask) {
        this.performanceTask = performanceTask;
    }

    public String getTargetMeasures() {
        return targetMeasures;
    }

    public void setTargetMeasures(String targetMeasures) {
        this.targetMeasures = targetMeasures;
    }

    public String getEvaluationCriteria() {
        return evaluationCriteria;
    }

    public void setEvaluationCriteria(String evaluationCriteria) {
        this.evaluationCriteria = evaluationCriteria;
    }

    public String getScoreWeight() {
        return scoreWeight;
    }

    public void setScoreWeight(String scoreWeight) {
        this.scoreWeight = scoreWeight;
    }

    public String getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(String completionTime) {
        this.completionTime = completionTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
