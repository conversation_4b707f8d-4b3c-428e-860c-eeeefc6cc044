package com.ruoyi.system.domain;

import java.util.Date;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 个人年度绩效计划共性指标表
 */
public class PersonnelYearlyCommonIndicator extends BaseEntity {
    private Long id;
    private Long planId;
    private String seqNo;
    private String responsibilityCategory;
    private String scoreWeight;
    private String completionTime;
    private Date createTime;
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(String seqNo) {
        this.seqNo = seqNo;
    }

    public String getResponsibilityCategory() {
        return responsibilityCategory;
    }

    public void setResponsibilityCategory(String responsibilityCategory) {
        this.responsibilityCategory = responsibilityCategory;
    }

    public String getScoreWeight() {
        return scoreWeight;
    }

    public void setScoreWeight(String scoreWeight) {
        this.scoreWeight = scoreWeight;
    }

    public String getCompletionTime() {
        return completionTime;
    }

    public void setCompletionTime(String completionTime) {
        this.completionTime = completionTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
