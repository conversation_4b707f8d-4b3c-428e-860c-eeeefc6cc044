package com.ruoyi.system.domain;

import java.util.Date;
import java.util.List;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 个人年度绩效计划主表
 */
public class PersonnelYearlyPlan extends BaseEntity {
    private Long id;
    private String name;
    private String department;
    private Integer year;
    private Date createTime;
    private Date updateTime;
    private String createBy;
    private String updateBy;
    
    // 关联的个性指标和共性指标
    private List<PersonnelYearlyPersonalIndicator> personalIndicators;
    private List<PersonnelYearlyCommonIndicator> commonIndicators;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public List<PersonnelYearlyPersonalIndicator> getPersonalIndicators() {
        return personalIndicators;
    }

    public void setPersonalIndicators(List<PersonnelYearlyPersonalIndicator> personalIndicators) {
        this.personalIndicators = personalIndicators;
    }

    public List<PersonnelYearlyCommonIndicator> getCommonIndicators() {
        return commonIndicators;
    }

    public void setCommonIndicators(List<PersonnelYearlyCommonIndicator> commonIndicators) {
        this.commonIndicators = commonIndicators;
    }
}
