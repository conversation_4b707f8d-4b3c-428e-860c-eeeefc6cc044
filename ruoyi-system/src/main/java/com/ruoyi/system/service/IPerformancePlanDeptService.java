package com.ruoyi.system.service;

import com.ruoyi.system.domain.PerformancePlanDept;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;

public interface IPerformancePlanDeptService {
    PerformancePlanDept selectPerformancePlanDeptById(Long id);
    List<PerformancePlanDept> selectPerformancePlanDeptList(PerformancePlanDept planDept);
    int insertPerformancePlanDept(PerformancePlanDept planDept);
    int updatePerformancePlanDept(PerformancePlanDept planDept);
    int deletePerformancePlanDeptByIds(Long[] ids);
    int deletePerformancePlanDeptById(Long id);
    List<PerformancePlanDept> selectPerformancePlanDeptListByIds(Long[] ids);
    void importWord(MultipartFile file) throws Exception;
    void exportWord(Long[] ids, HttpServletResponse response) throws Exception;
    void exportTemplate(HttpServletResponse response) throws Exception;

    /**
     * 导入Excel数据
     *
     * @param file Excel文件
     * @throws Exception 导入异常
     */
    void importExcelData(MultipartFile file) throws Exception;

    /**
     * 下载Excel模板
     *
     * @param response HTTP响应
     * @throws Exception 下载异常
     */
    void downloadExcelTemplate(HttpServletResponse response) throws Exception;
}