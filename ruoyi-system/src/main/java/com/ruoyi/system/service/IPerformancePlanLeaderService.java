package com.ruoyi.system.service;

import com.ruoyi.system.domain.PerformancePlanLeader;
import com.ruoyi.system.domain.PerformancePlanOrg;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface IPerformancePlanLeaderService {
    PerformancePlanLeader selectPerformancePlanLeaderById(Long id);
    List<PerformancePlanLeader> selectPerformancePlanLeaderList(PerformancePlanLeader plan);
    int insertPerformancePlanLeader(PerformancePlanLeader plan);
    int updatePerformancePlanLeader(PerformancePlanLeader plan);
    int deletePerformancePlanLeaderById(Long id);
    int deletePerformancePlanLeaderByIds(Long[] ids);
    int insertBatch(List<PerformancePlanLeader> plans);

    List<PerformancePlanLeader> selectPerformancePlanLeaderListByIds(Long[] ids);

    void importWord(MultipartFile file) throws Exception;
    void exportWord(Long[] ids, HttpServletResponse response) throws Exception;
    void exportTemplate(HttpServletResponse response) throws Exception;

    // Excel相关方法
    void importExcelData(MultipartFile file) throws Exception;
    void downloadExcelTemplate(HttpServletResponse response) throws Exception;
}