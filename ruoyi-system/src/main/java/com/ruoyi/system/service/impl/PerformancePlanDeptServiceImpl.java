package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.PerformancePlanDept;
import com.ruoyi.system.mapper.PerformancePlanDeptMapper;
import com.ruoyi.system.service.IPerformancePlanDeptService;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFFooter;

@Service
public class PerformancePlanDeptServiceImpl implements IPerformancePlanDeptService {
    @Autowired
    private PerformancePlanDeptMapper mapper;

    @Override
    public PerformancePlanDept selectPerformancePlanDeptById(Long id) {
        return mapper.selectPerformancePlanDeptById(id);
    }

    @Override
    public List<PerformancePlanDept> selectPerformancePlanDeptList(PerformancePlanDept performancePlanDept) {
        return mapper.selectPerformancePlanDeptList(performancePlanDept);
    }

    @Override
    public int insertPerformancePlanDept(PerformancePlanDept performancePlanDept) {
        return mapper.insertPerformancePlanDept(performancePlanDept);
    }

    @Override
    public int updatePerformancePlanDept(PerformancePlanDept performancePlanDept) {
        return mapper.updatePerformancePlanDept(performancePlanDept);
    }

    @Override
    public int deletePerformancePlanDeptById(Long id) {
        return mapper.deletePerformancePlanDeptById(id);
    }

    @Override
    public int deletePerformancePlanDeptByIds(Long[] ids) {
        return mapper.deletePerformancePlanDeptByIds(ids);
    }

    @Override
    public List<PerformancePlanDept> selectPerformancePlanDeptListByIds(Long[] ids) {
        if (ids == null || ids.length == 0) return new java.util.ArrayList<>();
        return mapper.selectPerformancePlanDeptListByIds(ids);
    }

    @Override
    public void importWord(MultipartFile file) throws Exception {
        List<PerformancePlanDept> plans = new ArrayList<>();
        XWPFDocument doc = new XWPFDocument(file.getInputStream());
        
        // 首先从文档中提取科室名称信息（用于占位符处理）
        String deptNameFromDocument = extractDeptNameFromDocument(doc);
        
        XWPFTable table = doc.getTables().get(0);
        // 跳过表头和示例行，从第3行开始
        for (int i = 2; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            if (row.getCell(0).getText().trim().isEmpty()) continue;
            PerformancePlanDept plan = new PerformancePlanDept();
            plan.setDeptName(deptNameFromDocument != null && !deptNameFromDocument.isEmpty() ? deptNameFromDocument : row.getCell(0).getText().trim());
            plan.setSeqNo(parseIntOrNull(row.getCell(1).getText().trim()));
            plan.setTaskType(row.getCell(2).getText().trim());
            plan.setTaskContent(row.getCell(3).getText().trim());
            plan.setTargetMeasure(row.getCell(4).getText().trim());
            plan.setEvalStandard(row.getCell(5).getText().trim());
            plan.setPrincipal(row.getCell(6).getText().trim());
            plan.setDeadline(row.getCell(7).getText().trim());
            plan.setScoreWeight(row.getCell(8).getText().trim());
            plans.add(plan);
        }
        for (PerformancePlanDept plan : plans) {
            mapper.insertPerformancePlanDept(plan);
        }
        doc.close();
    }
    
    /**
     * 从文档中提取科室名称信息
     */
    private String extractDeptNameFromDocument(XWPFDocument document) {
        // 在段落中查找科室名称
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null) {
                // 查找包含"科室"、"部门"等关键词的文本
                if (text.contains("科室：") || text.contains("部门：")) {
                    String deptName = "";
                    if (text.contains("科室：")) {
                        deptName = text.substring(text.indexOf("科室：") + 3).trim();
                    } else if (text.contains("部门：")) {
                        deptName = text.substring(text.indexOf("部门：") + 3).trim();
                    }
                    if (!deptName.isEmpty() && !deptName.contains("${")) {
                        return deptName;
                    }
                }
                if (text.matches(".*[\\u4e00-\\u9fa5]+[科部].*") && !text.contains("${")) {
                    String[] words = text.split("\\s+");
                    for (String word : words) {
                        if (word.matches(".*[\\u4e00-\\u9fa5]+[科部]")) {
                            return word.trim();
                        }
                    }
                }
            }
        }
        
        // 在表格中查找科室名称
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    String text = cell.getText();
                    if (text != null) {
                        if (text.contains("科室：") || text.contains("部门：")) {
                            String deptName = "";
                            if (text.contains("科室：")) {
                                deptName = text.substring(text.indexOf("科室：") + 3).trim();
                            } else if (text.contains("部门：")) {
                                deptName = text.substring(text.indexOf("部门：") + 3).trim();
                            }
                            if (!deptName.isEmpty() && !deptName.contains("${")) {
                                return deptName;
                            }
                        }
                    }
                }
            }
        }
        
        return null;
    }

    @Override
    public void exportWord(Long[] ids, HttpServletResponse response) throws Exception {
        List<PerformancePlanDept> plans = mapper.selectPerformancePlanDeptListByIds(ids);
        if (plans.isEmpty()) {
            throw new RuntimeException("没有找到要导出的数据");
        }
        
        // 读取模板
        InputStream templateStream = getClass().getResourceAsStream("/template/dept.docx");
        if (templateStream == null) {
            throw new RuntimeException("科室绩效计划模板文件不存在");
        }
        
        XWPFDocument doc = new XWPFDocument(templateStream);
        
        // 处理文档中的占位符（如左上角的科室名称）
        replacePlaceholders(doc, plans);
        
        List<XWPFTable> tables = doc.getTables();
        if (tables.isEmpty()) {
            throw new RuntimeException("模板文件中没有找到表格");
        }
        
        XWPFTable table = tables.get(0);
        
        // 找到数据插入位置：跳过表头和说明行
        int insertIndex = findDataInsertIndex(table);
        
        // 删除现有的数据行（保留表头和说明行）
        removeExistingDataRows(table, insertIndex);
        
        // 在指定位置插入数据行
        for (int i = 0; i < plans.size(); i++) {
            PerformancePlanDept plan = plans.get(i);
            XWPFTableRow row = table.insertNewTableRow(insertIndex + i);
            
            // 复制表头行的格式到新行
            copyRowFormat(table.getRow(0), row);
            
            // 确保有足够的单元格
            while (row.getTableCells().size() < 9) {
                row.createCell();
            }
            
            row.getCell(0).setText(plan.getDeptName() != null ? plan.getDeptName() : "");
            row.getCell(1).setText(plan.getSeqNo() != null ? plan.getSeqNo().toString() : "");
            row.getCell(2).setText(plan.getTaskType() != null ? plan.getTaskType() : "");
            row.getCell(3).setText(plan.getTaskContent() != null ? plan.getTaskContent() : "");
            row.getCell(4).setText(plan.getTargetMeasure() != null ? plan.getTargetMeasure() : "");
            row.getCell(5).setText(plan.getEvalStandard() != null ? plan.getEvalStandard() : "");
            row.getCell(6).setText(plan.getPrincipal() != null ? plan.getPrincipal() : "");
            row.getCell(7).setText(plan.getDeadline() != null ? plan.getDeadline() : "");
            row.getCell(8).setText(plan.getScoreWeight() != null ? plan.getScoreWeight() : "");
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=performance_plan_dept.docx");
        doc.write(response.getOutputStream());
        doc.close();
    }
    
    /**
     * 找到数据插入的位置（跳过表头和说明行）
     */
    private int findDataInsertIndex(XWPFTable table) {
        int rowCount = table.getRows().size();
        
        // 从第二行开始查找（跳过表头）
        for (int i = 1; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);
            
            // 更保守的策略：只跳过明确的说明行和提示行
            if (rowText.contains("填写说明") || 
                rowText.contains("注意事项") || 
                rowText.contains("备注：") ||
                rowText.contains("说明：") ||
                rowText.contains("请在此行填写") || 
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：")) {
                continue;
            }
            return i;
        }
        return rowCount;
    }
    
    /**
     * 删除现有的数据行（保留表头和说明行）
     */
    private void removeExistingDataRows(XWPFTable table, int startIndex) {
        int rowCount = table.getRows().size();
        
        // 从后往前删除，避免索引变化问题
        for (int i = rowCount - 1; i >= startIndex; i--) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);
            
            // 更保守的删除策略：只删除明确的提示行
            boolean shouldDelete = false;
            
            // 只删除包含明确提示语的行
            if (rowText.contains("请在此行填写") || 
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：") ||
                (rowText.contains("xxxx") && rowText.length() < 20) ||
                (rowText.contains("XXXX") && rowText.length() < 20) ||
                (rowText.contains("待填写") && rowText.length() < 20) ||
                (rowText.contains("请填写") && rowText.length() < 20)) {
                shouldDelete = true;
            }
            
            // 删除完全空的行（所有单元格都为空）
            if (rowText.trim().isEmpty()) {
                boolean allCellsEmpty = row.getTableCells().stream()
                    .allMatch(cell -> cell.getText().trim().isEmpty());
                if (allCellsEmpty) {
                    shouldDelete = true;
                }
            }
            
            // 执行删除
            if (shouldDelete) {
                table.removeRow(i);
            }
        }
    }
    
    /**
     * 复制行格式
     */
    private void copyRowFormat(XWPFTableRow sourceRow, XWPFTableRow targetRow) {
        while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
            targetRow.createCell();
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) throws Exception {
        InputStream templateStream = getClass().getResourceAsStream("/template/dept.docx");
        if (templateStream == null) {
            throw new FileNotFoundException("模板文件不存在");
        }
        
        try (XWPFDocument document = new XWPFDocument(templateStream)) {
            // 清理模板中的占位符
            cleanTemplateDocument(document);
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=dept_plan_template.docx");
            
            try (OutputStream outputStream = response.getOutputStream()) {
                document.write(outputStream);
                outputStream.flush();
            }
        }
    }
    
    /**
     * 清理模板文档中的占位符
     */
    private void cleanTemplateDocument(XWPFDocument document) {
        // 清理段落中的占位符
        document.getParagraphs().forEach(this::cleanParagraphPlaceholders);
        
        // 清理表格中的占位符
        document.getTables().forEach(table -> {
            table.getRows().forEach(row -> {
                row.getTableCells().forEach(cell -> {
                    cell.getParagraphs().forEach(this::cleanParagraphPlaceholders);
                });
            });
        });
        
        // 清理页眉页脚中的占位符
        document.getHeaderList().forEach(header -> {
            header.getParagraphs().forEach(this::cleanParagraphPlaceholders);
        });
        
        document.getFooterList().forEach(footer -> {
            footer.getParagraphs().forEach(this::cleanParagraphPlaceholders);
        });
    }
    
    /**
     * 清理段落中的占位符
     */
    private void cleanParagraphPlaceholders(XWPFParagraph paragraph) {
        paragraph.getRuns().forEach(run -> {
            String text = run.getText(0);
            if (text != null) {
                // 清理各种占位符
                text = text.replaceAll("\\$\\{.*?\\}", ""); // 清理所有占位符
                
                // 清理提示文本，但保留重要的描述信息
                if (text.contains("请在此行填写") || 
                    text.contains("提示") || 
                    text.contains("参考") || 
                    text.contains("格式") || 
                    text.contains("要求") || 
                    text.contains("xxxx") || 
                    text.contains("XXXX") || 
                    text.contains("待填写") || 
                    text.contains("请填写")) {
                    // 只有当整行都是提示文本时才清空，否则保留
                    if (text.length() < 50 && !text.contains("科室") && !text.contains("部门") && 
                        !text.contains("任务") && !text.contains("目标") && !text.contains("措施")) {
                        text = "";
                    }
                }
                
                run.setText(text, 0);
            }
        });
    }

    private Integer parseIntOrNull(String s) {
        try { return Integer.parseInt(s); } catch (Exception e) { return null; }
    }

    /**
     * 替换文档中的占位符
     */
    private void replacePlaceholders(XWPFDocument document, List<PerformancePlanDept> plans) {
        String deptName = plans.isEmpty() ? "" : (plans.get(0).getDeptName() != null ? plans.get(0).getDeptName() : "");
        
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphPlaceholders(paragraph, deptName);
        }
        
        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphPlaceholders(paragraph, deptName);
                    }
                }
            }
        }
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceParagraphPlaceholders(paragraph, deptName);
            }
        }
        
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceParagraphPlaceholders(paragraph, deptName);
            }
        }
    }
    
    /**
     * 替换段落中的占位符
     */
    private void replaceParagraphPlaceholders(XWPFParagraph paragraph, String deptName) {
        String text = paragraph.getText();
        if (text != null && text.contains("${")) {
            text = text.replace("${deptName}", deptName);

            for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }
            // 重新添加替换后的文本
            XWPFRun run = paragraph.createRun();
            run.setText(text);
        }
    }

    @Override
    public void importExcelData(MultipartFile file) throws Exception {
        List<PerformancePlanDept> plans = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 从第三行开始读取数据（第一行是标题，第二行是表头）
            for (int i = 3; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查第一列是否为空，如果为空则跳过
                Cell firstCell = row.getCell(0);
                if (firstCell == null || getCellValue(firstCell).trim().isEmpty()) {
                    continue;
                }

                PerformancePlanDept plan = new PerformancePlanDept();

                // 根据Excel列顺序读取数据
                plan.setSeqNo(parseIntegerFromCell(row.getCell(0))); // 序号
                plan.setTaskType(getCellValue(row.getCell(1))); // 任务类型
                plan.setTaskContent(getCellValue(row.getCell(2))); // 绩效任务
                plan.setTargetMeasure(getCellValue(row.getCell(3))); // 目标及措施
                plan.setEvalStandard(getCellValue(row.getCell(4))); // 评价标准
                plan.setPrincipal(getCellValue(row.getCell(5))); // 责任人
                plan.setDeadline(getCellValue(row.getCell(6))); // 完成时限
                plan.setScoreWeight(getCellValue(row.getCell(7))); // 分值及权重

                // 从文件名或第一行获取科室名称
                String deptName = extractDeptNameFromFile(sheet);
                plan.setDeptName(deptName);

                plans.add(plan);
            }
        }

        if (!plans.isEmpty()) {
            mapper.insertBatch(plans);
        }
    }

    @Override
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/template/class-plan.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("Excel模板文件不存在: /template/class-plan.xlsx");
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=class-plan-template.xlsx");

        // 直接将模板文件内容写入响应
        try (InputStream inputStream = templateStream;
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    /**
     * 获取单元格的字符串值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 从单元格解析整数值
     */
    private Integer parseIntegerFromCell(Cell cell) {
        String value = getCellValue(cell);
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从Excel文件中提取科室名称
     */
    private String extractDeptNameFromFile(Sheet sheet) {
        // 尝试从第一行获取科室名称
        Row firstRow = sheet.getRow(1);
        if (firstRow != null) {
            Cell firstCell = firstRow.getCell(0);
            if (firstCell != null) {
                String cellValue = getCellValue(firstCell);
                if (cellValue.contains("科室名称")) {
                    // 如果包含占位符，提取科室名称
                    String deptName = cellValue.replace("科室名称：", "").replace("${className}", "").trim();
                    if (!deptName.isEmpty()) {
                        return deptName;
                    }
                }
            }
        }

        // 如果无法从文件中提取，返回默认值
        return "未指定科室";
    }
}