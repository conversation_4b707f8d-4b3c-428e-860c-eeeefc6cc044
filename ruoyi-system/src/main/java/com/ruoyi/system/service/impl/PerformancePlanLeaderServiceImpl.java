package com.ruoyi.system.service.impl;

import com.ruoyi.system.domain.PerformancePlanLeader;
import com.ruoyi.system.mapper.PerformancePlanLeaderMapper;
import com.ruoyi.system.service.IPerformancePlanLeaderService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class PerformancePlanLeaderServiceImpl implements IPerformancePlanLeaderService {
    @Autowired
    private PerformancePlanLeaderMapper mapper;

    @Override
    public PerformancePlanLeader selectPerformancePlanLeaderById(Long id) {
        return mapper.selectPerformancePlanLeaderById(id);
    }

    @Override
    public List<PerformancePlanLeader> selectPerformancePlanLeaderList(PerformancePlanLeader performancePlanLeader) {
        return mapper.selectPerformancePlanLeaderList(performancePlanLeader);
    }

    @Override
    public int insertPerformancePlanLeader(PerformancePlanLeader performancePlanLeader) {
        return mapper.insertPerformancePlanLeader(performancePlanLeader);
    }

    @Override
    public int updatePerformancePlanLeader(PerformancePlanLeader performancePlanLeader) {
        return mapper.updatePerformancePlanLeader(performancePlanLeader);
    }

    @Override
    public int deletePerformancePlanLeaderById(Long id) {
        return mapper.deletePerformancePlanLeaderById(id);
    }

    @Override
    public int deletePerformancePlanLeaderByIds(Long[] ids) {
        return mapper.deletePerformancePlanLeaderByIds(ids);
    }

    @Override
    public int insertBatch(List<PerformancePlanLeader> plans) {
        if (plans == null || plans.isEmpty()) {
            return 0;
        }
        return mapper.insertBatch(plans);
    }

    @Override
    public List<PerformancePlanLeader> selectPerformancePlanLeaderListByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return new java.util.ArrayList<>();
        }
        return mapper.selectPerformancePlanLeaderListByIds(ids);
    }

    @Override
    public void importWord(MultipartFile file) throws Exception {
        List<PerformancePlanLeader> plans = new ArrayList<>();
        XWPFDocument doc = new XWPFDocument(file.getInputStream());
        
        // 首先从文档中提取姓名信息（用于占位符处理）
        String nameFromDocument = extractNameFromDocument(doc);
        
        XWPFTable table = doc.getTables().get(0);
        // 跳过表头和示例行，从第3行开始
        for (int i = 2; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            if (row.getCell(0).getText().trim().isEmpty()) {
                continue;
            }
            PerformancePlanLeader plan = new PerformancePlanLeader();
            plan.setName(nameFromDocument != null && !nameFromDocument.isEmpty() ? nameFromDocument : row.getCell(0).getText().trim());
            plan.setSeqNo(parseIntOrNull(row.getCell(1).getText().trim()));
            plan.setTaskType(row.getCell(2).getText().trim());
            plan.setTaskContent(row.getCell(3).getText().trim());
            plan.setTargetMeasure(row.getCell(4).getText().trim());
            plan.setEvalStandard(row.getCell(5).getText().trim());
            plan.setDutyType(row.getCell(6).getText().trim());
            plan.setScoreWeight(row.getCell(7).getText().trim());
            plan.setDeadline(row.getCell(8).getText().trim());
            plans.add(plan);
        }
        if (!plans.isEmpty()) {
            mapper.insertBatch(plans);
        }
        doc.close();
    }

    @Override
    public void exportWord(Long[] ids, HttpServletResponse response) throws Exception {
        List<PerformancePlanLeader> plans = mapper.selectPerformancePlanLeaderListByIds(ids);
        InputStream templateStream = getClass().getResourceAsStream("/template/theatrical.docx");
        if (templateStream == null) {
            throw new RuntimeException("模板文件不存在");
        }
        
        XWPFDocument doc = new XWPFDocument(templateStream);
        
        // 处理文档中的占位符（如左上角的姓名）
        replacePlaceholders(doc, plans);
        
        XWPFTable table = doc.getTables().get(0);

        int insertIndex = findDataInsertIndex(table);

        removeExistingDataRows(table, insertIndex);
        
        // 在指定位置插入数据行
        for (int i = 0; i < plans.size(); i++) {
            PerformancePlanLeader plan = plans.get(i);
            XWPFTableRow row = table.insertNewTableRow(insertIndex + i);

            copyRowFormat(table.getRow(0), row);

            while (row.getTableCells().size() < 9) {
                row.createCell();
            }
            
            // 根据新的字段映射填充数据
            row.getCell(0).setText(plan.getTaskType() != null ? plan.getTaskType() : "");
            row.getCell(1).setText(plan.getPerformanceTask() != null ? plan.getPerformanceTask() : "");
            row.getCell(2).setText(plan.getTargetMeasures() != null ? plan.getTargetMeasures() : "");
            row.getCell(3).setText(plan.getEvaluationCriteria() != null ? plan.getEvaluationCriteria() : "");
            row.getCell(4).setText(plan.getTaskCategory() != null ? plan.getTaskCategory() : "");
            row.getCell(5).setText(plan.getWeightScore() != null ? plan.getWeightScore() : "");
            row.getCell(6).setText(plan.getDeadline() != null ? plan.getDeadline() : "");
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        response.setHeader("Content-Disposition", "attachment; filename=performance_plan_leader.docx");
        doc.write(response.getOutputStream());
        doc.close();
    }

    /**
     * 找到数据插入的位置（跳过表头和说明行）
     */
    private int findDataInsertIndex(XWPFTable table) {
        int rowCount = table.getRows().size();

        // 从第二行开始查找（跳过表头）
        for (int i = 1; i < rowCount; i++) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);

            // 更保守的策略：只跳过明确的说明行和提示行
            if (rowText.contains("填写说明") ||
                rowText.contains("注意事项") ||
                rowText.contains("备注：") ||
                rowText.contains("说明：") ||
                rowText.contains("请在此行填写") ||
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：")) {
                continue;
            }
            return i;
        }
        return rowCount;
    }
    
    /**
     * 删除现有的数据行（保留表头和说明行）
     */
    private void removeExistingDataRows(XWPFTable table, int startIndex) {
        int rowCount = table.getRows().size();
        
        // 从后往前删除，避免索引变化问题
        for (int i = rowCount - 1; i >= startIndex; i--) {
            XWPFTableRow row = table.getRow(i);
            String rowText = row.getTableCells().stream()
                .map(cell -> cell.getText())
                .reduce("", (a, b) -> a + b);
            
            // 更保守的删除策略：只删除明确的提示行
            boolean shouldDelete = false;
            
            // 只删除包含明确提示语的行
            if (rowText.contains("请在此行填写") || 
                rowText.contains("请在此处填写") ||
                rowText.contains("请填写相关内容") ||
                rowText.contains("示例：") ||
                rowText.contains("例如：") ||
                (rowText.contains("xxxx") && rowText.length() < 20) ||
                (rowText.contains("XXXX") && rowText.length() < 20) ||
                (rowText.contains("待填写") && rowText.length() < 20) ||
                (rowText.contains("请填写") && rowText.length() < 20)) {
                shouldDelete = true;
            }
            
            // 删除完全空的行（所有单元格都为空）
            if (rowText.trim().isEmpty()) {
                boolean allCellsEmpty = row.getTableCells().stream()
                    .allMatch(cell -> cell.getText().trim().isEmpty());
                if (allCellsEmpty) {
                    shouldDelete = true;
                }
            }
            
            // 执行删除
            if (shouldDelete) {
                table.removeRow(i);
            }
        }
    }
    
    /**
     * 复制行格式
     */
    private void copyRowFormat(XWPFTableRow sourceRow, XWPFTableRow targetRow) {
        while (targetRow.getTableCells().size() < sourceRow.getTableCells().size()) {
            targetRow.createCell();
        }
    }

    @Override
    public void exportTemplate(HttpServletResponse response) throws Exception {
        InputStream templateStream = getClass().getResourceAsStream("/template/theatrical.docx");
        if (templateStream == null) {
            throw new FileNotFoundException("模板文件不存在");
        }
        
        try (XWPFDocument document = new XWPFDocument(templateStream)) {
            cleanTemplateDocument(document);
            
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=leader_plan_template.docx");
            
            try (OutputStream outputStream = response.getOutputStream()) {
                document.write(outputStream);
                outputStream.flush();
            }
        }
    }
    
    /**
     * 清理模板文档中的占位符
     */
    private void cleanTemplateDocument(XWPFDocument document) {
        // 清理段落中的占位符
        document.getParagraphs().forEach(this::cleanParagraphPlaceholders);
        
        // 清理表格中的占位符
        document.getTables().forEach(table -> {
            table.getRows().forEach(row -> {
                row.getTableCells().forEach(cell -> {
                    cell.getParagraphs().forEach(this::cleanParagraphPlaceholders);
                });
            });
        });
    }
    
    /**
     * 清理段落中的占位符
     */
    private void cleanParagraphPlaceholders(XWPFParagraph paragraph) {
        paragraph.getRuns().forEach(run -> {
            String text = run.getText(0);
            if (text != null) {
                // 清理各种占位符
                text = text.replaceAll("\\$\\{.*?\\}", ""); // 清理所有占位符
                
                // 清理提示文本
                if (text.contains("请在此行填写") || 
                    text.contains("提示") || 
                    text.contains("参考") || 
                    text.contains("格式") || 
                    text.contains("要求") || 
                    text.contains("xxxx") || 
                    text.contains("XXXX") || 
                    text.contains("待填写") || 
                    text.contains("请填写")) {
                    text = "";
                }
                
                run.setText(text, 0);
            }
        });
    }

    /**
     * 从文档中提取姓名信息
     */
    private String extractNameFromDocument(XWPFDocument document) {
        // 在段落中查找姓名
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            String text = paragraph.getText();
            if (text != null && text.contains("姓名：")) {
                String name = text.substring(text.indexOf("姓名：") + 3).trim();
                if (!name.isEmpty() && !name.contains("${")) {
                    return name;
                }
            }
        }
        
        // 在表格中查找姓名
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    String text = cell.getText();
                    if (text != null && text.contains("姓名：")) {
                        String name = text.substring(text.indexOf("姓名：") + 3).trim();
                        if (!name.isEmpty() && !name.contains("${")) {
                            return name;
                        }
                    }
                }
            }
        }
        
        return null;
    }
    
    /**
     * 替换文档中的占位符
     */
    private void replacePlaceholders(XWPFDocument document, List<PerformancePlanLeader> plans) {
        // 获取姓名（取第一个计划的姓名，假设同一批导出的都是同一个人）
        String name = plans.isEmpty() ? "" : (plans.get(0).getName() != null ? plans.get(0).getName() : "");
        
        // 替换段落中的占位符
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphPlaceholders(paragraph, name);
        }
        
        // 替换表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphPlaceholders(paragraph, name);
                    }
                }
            }
        }
        
        // 替换页眉页脚中的占位符
        for (XWPFHeader header : document.getHeaderList()) {
            for (XWPFParagraph paragraph : header.getParagraphs()) {
                replaceParagraphPlaceholders(paragraph, name);
            }
        }
        
        for (XWPFFooter footer : document.getFooterList()) {
            for (XWPFParagraph paragraph : footer.getParagraphs()) {
                replaceParagraphPlaceholders(paragraph, name);
            }
        }
    }
    
    /**
     * 替换段落中的占位符
     */
    private void replaceParagraphPlaceholders(XWPFParagraph paragraph, String name) {
        String text = paragraph.getText();
        if (text != null && text.contains("${")) {
            text = text.replace("${name}", name);
            
            for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }
            
            // 重新添加替换后的文本
            XWPFRun run = paragraph.createRun();
            run.setText(text);
        }
    }

    private Integer parseIntOrNull(String s) {
        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void importExcelData(MultipartFile file) throws Exception {
        List<PerformancePlanLeader> plans = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            // 从第三行开始读取数据（第一行是标题，第二行是表头）
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 检查第一列是否为空，如果为空则跳过
                Cell firstCell = row.getCell(0);
                if (firstCell == null || getCellValue(firstCell).trim().isEmpty()) {
                    continue;
                }

                PerformancePlanLeader plan = new PerformancePlanLeader();

                // 根据Excel模板的列顺序读取数据
                // 序号、任务类型、绩效任务、目标及措施、评价标准、责任分类、权重分值、完成时限
                plan.setSeqNo(parseIntOrNull(getCellValue(row.getCell(0))));
                plan.setTaskType(getCellValue(row.getCell(1)));
                plan.setPerformanceTask(getCellValue(row.getCell(2)));
                plan.setTargetMeasures(getCellValue(row.getCell(3)));
                plan.setEvaluationCriteria(getCellValue(row.getCell(4)));
                plan.setTaskCategory(getCellValue(row.getCell(5)));
                plan.setWeightScore(getCellValue(row.getCell(6)));
                plan.setDeadline(getCellValue(row.getCell(7)));

                // 设置默认值
                plan.setName("${memberName}"); // 占位符，实际使用时需要替换

                plans.add(plan);
            }

            if (!plans.isEmpty()) {
                mapper.insertBatch(plans);
            }
        }
    }

    @Override
    public void downloadExcelTemplate(HttpServletResponse response) throws Exception {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/template/member-plan.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("Excel模板文件不存在: /template/member-plan.xlsx");
        }

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=member-plan-template.xlsx");

        // 直接将模板文件内容写入响应
        try (InputStream inputStream = templateStream;
             OutputStream outputStream = response.getOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}